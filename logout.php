<?php
require_once 'config/config.php';

// Check if user is logged in
if (isAuthenticated()) {
    // Destroy all session data
    session_destroy();
    
    // Clear session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
}

// Redirect to login page with success message
redirect('login.php?logout=success');
?>
