<?php
// Auto-detect database configuration
$db_configs = [
    // Common MAMP configurations
    ['host' => 'localhost', 'user' => 'root', 'pass' => '', 'name' => 'elearning_platform'],
    ['host' => 'localhost', 'user' => 'root', 'pass' => 'root', 'name' => 'elearning_platform'],
    ['host' => '127.0.0.1', 'user' => 'root', 'pass' => '', 'name' => 'elearning_platform'],
    ['host' => '127.0.0.1', 'user' => 'root', 'pass' => 'root', 'name' => 'elearning_platform'],
    ['host' => 'localhost:3306', 'user' => 'root', 'pass' => '', 'name' => 'elearning_platform'],
    ['host' => 'localhost:3306', 'user' => 'root', 'pass' => 'root', 'name' => 'elearning_platform'],
    ['host' => 'localhost:8889', 'user' => 'root', 'pass' => 'root', 'name' => 'elearning_platform'], // MAMP default
];

$conn = null;
$successful_config = null;

foreach ($db_configs as $config) {
    try {
        $dsn = "mysql:host={$config['host']};charset=utf8mb4";
        $test_conn = new PDO($dsn, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]);
        
        // Try to create database if it doesn't exist
        $test_conn->exec("CREATE DATABASE IF NOT EXISTS {$config['name']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // Connect to the specific database
        $dsn_with_db = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
        $conn = new PDO($dsn_with_db, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]);
        
        $successful_config = $config;
        break; // Success! Exit the loop
        
    } catch (PDOException $e) {
        // Continue to next configuration
        continue;
    }
}

if (!$conn) {
    error_log("All database connection attempts failed");
    die("عذراً، تعذر الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات MAMP.");
}

// Define constants based on successful configuration
define('DB_HOST', $successful_config['host']);
define('DB_USER', $successful_config['user']);
define('DB_PASS', $successful_config['pass']);
define('DB_NAME', $successful_config['name']);

// Helper functions for database operations
function query($sql, $params = []) {
    global $conn;
    try {
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        error_log("Query failed: " . $e->getMessage());
        return false;
    }
}

function fetch($sql, $params = []) {
    $stmt = query($sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

function fetchAll($sql, $params = []) {
    $stmt = query($sql, $params);
    return $stmt ? $stmt->fetchAll() : false;
}

function insert($table, $data) {
    global $conn;
    try {
        $columns = implode(', ', array_keys($data));
        $values = implode(', ', array_fill(0, count($data), '?'));
        $sql = "INSERT INTO $table ($columns) VALUES ($values)";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute(array_values($data));
        return $conn->lastInsertId();
    } catch(PDOException $e) {
        error_log("Insert failed: " . $e->getMessage());
        return false;
    }
}

function update($table, $data, $where, $whereParams = []) {
    global $conn;
    try {
        $set = implode(' = ?, ', array_keys($data)) . ' = ?';
        $sql = "UPDATE $table SET $set WHERE $where";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute(array_merge(array_values($data), $whereParams));
        return $stmt->rowCount();
    } catch(PDOException $e) {
        error_log("Update failed: " . $e->getMessage());
        return false;
    }
}

function delete($table, $where, $params = []) {
    global $conn;
    try {
        $sql = "DELETE FROM $table WHERE $where";
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->rowCount();
    } catch(PDOException $e) {
        error_log("Delete failed: " . $e->getMessage());
        return false;
    }
}

// Transaction helper functions
function beginTransaction() {
    global $conn;
    return $conn->beginTransaction();
}

function commit() {
    global $conn;
    return $conn->commit();
}

function rollback() {
    global $conn;
    return $conn->rollBack();
}

// Log successful connection for debugging
error_log("Database connected successfully with config: " . json_encode($successful_config));
?>
