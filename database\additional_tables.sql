-- Additional tables for index.php functionality

-- Newsletter subscriptions table
CREATE TABLE IF NOT EXISTS newsletter_subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(100) UNIQUE NOT NULL,
    status ENUM('active', 'unsubscribed') DEFAULT 'active',
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP NULL,
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- Testimonials table
CREATE TABLE IF NOT EXISTS testimonials (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    course_id INT,
    name VARCHAR(100) NOT NULL,
    position VARCHAR(100),
    avatar VARCHAR(255),
    content TEXT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    is_featured BOOLEAN DEFAULT FALSE,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
    INDEX idx_featured (is_featured),
    INDEX idx_status (status)
);

-- Site statistics table
CREATE TABLE IF NOT EXISTS site_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    stat_name VARCHAR(50) UNIQUE NOT NULL,
    stat_value INT NOT NULL DEFAULT 0,
    display_value VARCHAR(20),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert initial statistics
INSERT INTO site_statistics (stat_name, stat_value, display_value) VALUES
('total_students', 15000, '15,000+'),
('total_courses', 500, '500+'),
('total_instructors', 150, '150+'),
('satisfaction_rate', 98, '98%')
ON DUPLICATE KEY UPDATE 
stat_value = VALUES(stat_value),
display_value = VALUES(display_value);

-- Insert sample categories
INSERT INTO categories (name, slug, description) VALUES
('البرمجة وتطوير المواقع', 'programming', 'تعلم لغات البرمجة وتطوير المواقع والتطبيقات'),
('التصميم والجرافيك', 'design', 'تصميم الجرافيك والواجهات والهويات البصرية'),
('إدارة الأعمال', 'business', 'مهارات الإدارة والقيادة وريادة الأعمال'),
('التسويق الرقمي', 'marketing', 'استراتيجيات التسويق الرقمي ووسائل التواصل'),
('تحليل البيانات', 'data-analysis', 'علوم البيانات والذكاء الاصطناعي'),
('اللغات', 'languages', 'تعلم اللغات المختلفة'),
('التصوير والمونتاج', 'photography', 'فنون التصوير وتحرير الفيديو'),
('الموسيقى والفنون', 'music', 'تعلم الموسيقى والفنون الإبداعية')
ON DUPLICATE KEY UPDATE 
name = VALUES(name),
description = VALUES(description);

-- Insert sample instructors
INSERT INTO users (username, email, password, full_name, role, bio, profile_image) VALUES
('ahmed_mohamed', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد محمد', 'instructor', 'مطور ويب خبير مع أكثر من 10 سنوات من الخبرة في تطوير المواقع والتطبيقات', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'),
('fatima_ahmed', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'فاطمة أحمد', 'instructor', 'مصممة جرافيك محترفة متخصصة في التصميم الرقمي والهويات البصرية', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'),
('mohamed_ali', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد علي', 'instructor', 'خبير تسويق رقمي مع خبرة واسعة في إدارة الحملات الإعلانية', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'),
('khalid_saeed', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'خالد السعيد', 'instructor', 'محلل بيانات ومختص في الذكاء الاصطناعي وعلوم البيانات', 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150&h=150&fit=crop&crop=face'),
('nora_hassan', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'نورا حسن', 'instructor', 'خبيرة في إدارة الأعمال والموارد البشرية مع خبرة 15 سنة', 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face'),
('youssef_ahmed', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'يوسف أحمد', 'instructor', 'مدرس لغة إنجليزية معتمد مع خبرة في التدريس التفاعلي', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face')
ON DUPLICATE KEY UPDATE 
full_name = VALUES(full_name),
bio = VALUES(bio),
profile_image = VALUES(profile_image);

-- Insert sample courses
INSERT INTO courses (title, slug, description, instructor_id, category_id, price, discount_price, thumbnail, level, language, duration, total_lessons, enrolled_students, rating, status) VALUES
('دورة تطوير المواقع الشاملة', 'web-development-complete', 'تعلم تطوير المواقع من الصفر حتى الاحتراف. دورة شاملة تغطي HTML, CSS, JavaScript, PHP وقواعد البيانات.', 1, 1, 499.00, 299.00, 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&h=600&fit=crop', 'beginner', 'Arabic', 720, 45, 1234, 4.9, 'published'),
('أساسيات التصميم الجرافيكي', 'graphic-design-basics', 'تعلم أساسيات التصميم الجرافيكي باستخدام Adobe Photoshop و Illustrator. دورة شاملة للمبتدئين.', 2, 2, 199.00, NULL, 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&h=600&fit=crop', 'beginner', 'Arabic', 480, 32, 856, 4.7, 'published'),
('استراتيجيات التسويق الرقمي', 'digital-marketing-strategies', 'تعلم أحدث استراتيجيات التسويق الرقمي وإدارة الحملات الإعلانية على منصات التواصل الاجتماعي.', 3, 4, 399.00, 249.00, 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop', 'intermediate', 'Arabic', 900, 60, 2156, 4.8, 'published'),
('مقدمة في علوم البيانات', 'data-science-intro', 'تعلم أساسيات علوم البيانات وتحليل البيانات باستخدام Python و SQL.', 4, 5, 349.00, NULL, 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop', 'intermediate', 'Arabic', 1200, 80, 743, 4.6, 'published'),
('أساسيات إدارة الأعمال', 'business-management-basics', 'تعلم أساسيات إدارة الأعمال والقيادة الفعالة في بيئة العمل الحديثة.', 5, 3, 0.00, NULL, 'https://images.unsplash.com/photo-1553877522-43269d4ea984?w=800&h=600&fit=crop', 'beginner', 'Arabic', 360, 24, 1567, 4.7, 'published'),
('اللغة الإنجليزية للمبتدئين', 'english-for-beginners', 'تعلم اللغة الإنجليزية من الأساسيات مع التركيز على المحادثة والقواعد الأساسية.', 6, 6, 249.00, 149.00, 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&h=600&fit=crop', 'beginner', 'Arabic', 600, 40, 3421, 4.9, 'published')
ON DUPLICATE KEY UPDATE 
title = VALUES(title),
description = VALUES(description),
price = VALUES(price),
discount_price = VALUES(discount_price),
thumbnail = VALUES(thumbnail);

-- Insert sample testimonials
INSERT INTO testimonials (user_id, course_id, name, position, avatar, content, rating, is_featured, status) VALUES
(1, 1, 'أحمد محمد', 'مطور ويب', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face', 'منصة رائعة ساعدتني في تطوير مهاراتي في البرمجة. المحتوى عالي الجودة والمدربين محترفين جداً.', 5, TRUE, 'approved'),
(2, 2, 'فاطمة أحمد', 'مصممة جرافيك', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face', 'تعلمت التصميم الجرافيكي من الصفر وأصبحت الآن أعمل كمصممة مستقلة. شكراً لهذه المنصة الرائعة.', 5, TRUE, 'approved'),
(3, 3, 'محمد علي', 'مختص تسويق رقمي', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face', 'دورات التسويق الرقمي غيرت مسار حياتي المهنية. المحتوى عملي ومفيد جداً للتطبيق الفوري.', 5, TRUE, 'approved')
ON DUPLICATE KEY UPDATE 
content = VALUES(content),
rating = VALUES(rating);
