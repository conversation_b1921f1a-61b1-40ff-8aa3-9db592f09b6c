<?php
// Application configuration
define('APP_NAME', 'منصة التعليم التفاعلي');
define('APP_URL', 'http://localhost/التعليم التفاعلي الالكتروتي');
define('APP_VERSION', '1.0.0');

// Directory paths
define('ROOT_PATH', dirname(__DIR__));
define('UPLOAD_PATH', ROOT_PATH . '/uploads');
define('COURSE_IMAGES_PATH', UPLOAD_PATH . '/courses');
define('PROFILE_IMAGES_PATH', UPLOAD_PATH . '/profiles');
define('LESSON_MATERIALS_PATH', UPLOAD_PATH . '/materials');

// Create required directories if they don't exist
$directories = [
    UPLOAD_PATH,
    COURSE_IMAGES_PATH,
    PROFILE_IMAGES_PATH,
    LESSON_MATERIALS_PATH
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0777, true);
    }
}

// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', ROOT_PATH . '/logs/error.log');

// Time zone
date_default_timezone_set('Africa/Cairo');

// Security settings
define('HASH_COST', 12); // For password hashing
define('TOKEN_EXPIRY', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_TIMEOUT', 900); // 15 minutes

// File upload settings
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_IMAGE_TYPES', ['image/jpeg', 'image/png', 'image/gif']);
define('ALLOWED_DOCUMENT_TYPES', ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);
define('ALLOWED_VIDEO_TYPES', ['video/mp4', 'video/webm']);

// Email configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', APP_NAME);

// Payment gateway settings
define('PAYMENT_GATEWAY', 'stripe'); // or 'paypal'
define('STRIPE_PUBLIC_KEY', 'your-stripe-public-key');
define('STRIPE_SECRET_KEY', 'your-stripe-secret-key');
define('PAYPAL_CLIENT_ID', 'your-paypal-client-id');
define('PAYPAL_SECRET', 'your-paypal-secret');

// Cache settings
define('CACHE_ENABLED', true);
define('CACHE_PATH', ROOT_PATH . '/cache');
define('CACHE_EXPIRY', 3600); // 1 hour

// Helper functions
function config($key) {
    return defined($key) ? constant($key) : null;
}

function asset($path) {
    return APP_URL . '/assets/' . ltrim($path, '/');
}

function url($path = '') {
    return APP_URL . '/' . ltrim($path, '/');
}

function redirect($path) {
    header('Location: ' . url($path));
    exit;
}

function isAuthenticated() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function isInstructor() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'instructor';
}

function csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Load environment variables if .env file exists
if (file_exists(ROOT_PATH . '/.env')) {
    $env = parse_ini_file(ROOT_PATH . '/.env');
    foreach ($env as $key => $value) {
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
} 