/* Instructors Page Styles */

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 60vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

/* Instructor Cards */
.instructor-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-radius: 20px;
    overflow: hidden;
    background: white;
}

.instructor-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.instructor-avatar {
    width: 120px;
    height: 120px;
    border: 5px solid #fff;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    object-fit: cover;
}

.instructor-card:hover .instructor-avatar {
    transform: scale(1.1);
}

/* Stats Badges */
.stats-badge {
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    margin: 2px;
    display: inline-block;
    font-weight: 500;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

/* Floating Shapes Animation */
.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.shape {
    position: absolute;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) { 
    width: 80px; 
    height: 80px; 
    top: 20%; 
    left: 10%; 
    animation-delay: 0s; 
}

.shape:nth-child(2) { 
    width: 60px; 
    height: 60px; 
    top: 60%; 
    left: 80%; 
    animation-delay: 2s; 
}

.shape:nth-child(3) { 
    width: 100px; 
    height: 100px; 
    top: 80%; 
    left: 20%; 
    animation-delay: 4s; 
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Search Section */
.search-section {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
}

/* Rating Stars */
.rating-stars .fa-star {
    font-size: 1.1rem;
    margin: 0 1px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 50vh;
        text-align: center;
    }
    
    .hero-section .display-3 {
        font-size: 2.5rem;
    }
    
    .instructor-card {
        margin-bottom: 2rem;
    }
    
    .stats-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
    }
}

/* Loading Animation */
.instructor-card {
    opacity: 1;
    transform: translateY(0);
}

/* Button Hover Effects */
.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-color: transparent;
    transform: translateY(-2px);
}

/* Navbar Enhancements */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #667eea !important;
}

.nav-link.active {
    color: #667eea !important;
    font-weight: 600;
}

/* Professional Footer Styles */
.professional-footer {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    position: relative;
    overflow: hidden;
}

.professional-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(45deg, #667eea, #764ba2);
    padding: 3rem 0;
    position: relative;
}

.newsletter-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.newsletter-form {
    max-width: 400px;
    margin-left: auto;
}

.newsletter-input {
    border: none;
    border-radius: 50px 0 0 50px;
    padding: 15px 20px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.95);
}

.newsletter-input:focus {
    box-shadow: none;
    border-color: transparent;
    background: white;
}

.btn-newsletter {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    border: none;
    border-radius: 0 50px 50px 0;
    padding: 15px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-newsletter:hover {
    background: linear-gradient(45deg, #f5576c, #f093fb);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
    color: white;
}

/* Main Footer */
.footer-main {
    padding: 4rem 0 2rem;
    position: relative;
}

.footer-widget {
    height: 100%;
}

.footer-title {
    color: white;
    font-weight: 700;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
}

.footer-desc {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.8;
    font-size: 0.95rem;
}

/* Footer Stats */
.footer-stats {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
}

.stat-item h5 {
    font-weight: 700;
    font-size: 1.5rem;
}

/* Social Links */
.social-links .social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s ease;
}

.social-link.facebook::before { background: #3b5998; }
.social-link.twitter::before { background: #1da1f2; }
.social-link.instagram::before { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }
.social-link.linkedin::before { background: #0077b5; }
.social-link.youtube::before { background: #ff0000; }
.social-link.telegram::before { background: #0088cc; }

.social-link:hover::before {
    transform: scale(1);
}

.social-link:hover {
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    font-size: 0.95rem;
}

.footer-links a:hover {
    color: #667eea;
    transform: translateX(5px);
}

.footer-links a i {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Contact Info */
.contact-info .contact-item {
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 10px;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.contact-info h6 {
    font-size: 0.9rem;
    font-weight: 600;
}

.contact-info a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-info a:hover {
    color: #667eea;
}

/* App Download */
.app-download .app-btn {
    display: block;
    transition: transform 0.3s ease;
}

.app-download .app-btn:hover {
    transform: scale(1.05);
}

.app-download img {
    height: 40px;
    border-radius: 8px;
}

/* Footer Bottom */
.footer-bottom {
    background: rgba(0, 0, 0, 0.3);
    padding: 1.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-bottom-link:hover {
    color: #667eea;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
}
