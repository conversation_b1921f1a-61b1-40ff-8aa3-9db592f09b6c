/* Professional Footer Styles */

/* Main Footer Container */
.professional-footer {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    position: relative;
    overflow: hidden;
    margin-top: auto;
}

.professional-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(45deg, #667eea, #764ba2);
    padding: 3rem 0;
    position: relative;
    overflow: hidden;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translate(0, 0); }
    100% { transform: translate(-20px, -20px); }
}

.newsletter-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.newsletter-icon:hover {
    transform: scale(1.1) rotate(10deg);
    background: rgba(255, 255, 255, 0.3);
}

.newsletter-form {
    max-width: 400px;
    margin-left: auto;
}

.newsletter-input {
    border: none;
    border-radius: 50px 0 0 50px;
    padding: 15px 20px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.newsletter-input:focus {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
    border-color: transparent;
    background: white;
    outline: none;
}

.btn-newsletter {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    border: none;
    border-radius: 0 50px 50px 0;
    padding: 15px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-newsletter::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.btn-newsletter:hover::before {
    left: 100%;
}

.btn-newsletter:hover {
    background: linear-gradient(45deg, #f5576c, #f093fb);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
    color: white;
}

/* Main Footer Content */
.footer-main {
    padding: 4rem 0 2rem;
    position: relative;
}

.footer-widget {
    height: 100%;
}

.footer-title {
    color: white;
    font-weight: 700;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
    font-size: 1.2rem;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.footer-widget:hover .footer-title::after {
    width: 80px;
}

.footer-desc {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.8;
    font-size: 0.95rem;
    text-align: justify;
}

/* Footer Stats */
.footer-stats {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.footer-stats:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-5px);
}

.stat-item {
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: scale(1.1);
}

.stat-item h5 {
    font-weight: 700;
    font-size: 1.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Social Links */
.social-links .social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s ease;
    z-index: -1;
}

.social-link.facebook::before { background: #3b5998; }
.social-link.twitter::before { background: #1da1f2; }
.social-link.instagram::before { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }
.social-link.linkedin::before { background: #0077b5; }
.social-link.youtube::before { background: #ff0000; }
.social-link.telegram::before { background: #0088cc; }

.social-link:hover::before {
    transform: scale(1);
}

.social-link:hover {
    color: white;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
    transition: all 0.3s ease;
}

.footer-links li:hover {
    transform: translateX(10px);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    padding: 0.3rem 0;
    border-radius: 5px;
}

.footer-links a:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    padding-left: 10px;
}

.footer-links a i {
    font-size: 0.8rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.footer-links a:hover i {
    opacity: 1;
    transform: scale(1.2);
}

/* Contact Info */
.contact-info .contact-item {
    transition: all 0.3s ease;
    padding: 0.8rem;
    border-radius: 10px;
    margin-bottom: 0.5rem;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(10px);
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.contact-item:hover .contact-icon {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.contact-info h6 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.contact-info a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-info a:hover {
    color: #667eea;
}

/* App Download */
.app-download .app-btn {
    display: block;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.app-download .app-btn:hover {
    transform: scale(1.05) translateY(-2px);
}

.app-download img {
    height: 40px;
    border-radius: 8px;
    filter: brightness(0.9);
    transition: filter 0.3s ease;
}

.app-download .app-btn:hover img {
    filter: brightness(1);
}

/* Footer Bottom */
.footer-bottom {
    background: rgba(0, 0, 0, 0.3);
    padding: 1.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.footer-bottom-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    padding: 0.3rem 0.5rem;
    border-radius: 5px;
}

.footer-bottom-link:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .newsletter-section {
        padding: 2rem 0;
        text-align: center;
    }
    
    .newsletter-form {
        margin: 1rem auto 0;
    }
    
    .footer-main {
        padding: 3rem 0 1.5rem;
    }
    
    .footer-stats {
        margin-bottom: 2rem;
    }
    
    .social-links {
        text-align: center;
        margin-top: 1.5rem;
    }
    
    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
    
    .app-download {
        text-align: center;
    }
}
