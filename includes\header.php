<?php
// Get current page for active menu item
$current_page = basename($_SERVER['PHP_SELF']);
?>
<header class="bg-white shadow-sm">
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="assets/images/logo.png" alt="<?php echo APP_NAME; ?>" height="40">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page === 'index.php' ? 'active' : ''; ?>" href="index.php">
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page === 'courses.php' ? 'active' : ''; ?>" href="courses.php">
                            الدورات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page === 'instructors.php' ? 'active' : ''; ?>" href="instructors.php">
                            المدرسون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page === 'about.php' ? 'active' : ''; ?>" href="about.php">
                            من نحن
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page === 'contact.php' ? 'active' : ''; ?>" href="contact.php">
                            اتصل بنا
                        </a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Search Form -->
                    <form class="d-none d-lg-flex me-3" action="courses.php" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" placeholder="ابحث عن دورة...">
                            <button class="btn btn-outline-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                    
                    <?php if (isAuthenticated()): ?>
                        <!-- User Menu -->
                        <div class="dropdown">
                            <button class="btn btn-link text-dark text-decoration-none dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <img src="<?php echo $_SESSION['user_image'] ?: 'assets/images/avatar-placeholder.jpg'; ?>" 
                                     class="rounded-circle me-2" width="32" height="32" alt="<?php echo $_SESSION['user_name']; ?>">
                                <?php echo $_SESSION['user_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <?php if (isAdmin()): ?>
                                    <li>
                                        <a class="dropdown-item" href="admin/dashboard.php">
                                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                                        </a>
                                    </li>
                                <?php elseif (isInstructor()): ?>
                                    <li>
                                        <a class="dropdown-item" href="instructor/dashboard.php">
                                            <i class="fas fa-chalkboard-teacher me-2"></i> لوحة المدرس
                                        </a>
                                    </li>
                                <?php else: ?>
                                    <li>
                                        <a class="dropdown-item" href="student/dashboard.php">
                                            <i class="fas fa-user-graduate me-2"></i> لوحة الطالب
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <li>
                                    <a class="dropdown-item" href="profile.php">
                                        <i class="fas fa-user me-2"></i> الملف الشخصي
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="settings.php">
                                        <i class="fas fa-cog me-2"></i> الإعدادات
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="logout.php">
                                        <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                                    </a>
                                </li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <!-- Auth Buttons -->
                        <a href="login.php" class="btn btn-outline-primary me-2">تسجيل الدخول</a>
                        <a href="register.php" class="btn btn-primary">إنشاء حساب</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>
</header> 