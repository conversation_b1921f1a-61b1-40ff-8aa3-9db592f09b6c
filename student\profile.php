<?php
session_start();
require_once '../config/config.php';
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$success = '';
$error = '';

// Get user data from database
try {
    $user = fetch("SELECT * FROM users WHERE id = ?", [$user_id]);
    if (!$user) {
        $error = 'لم يتم العثور على بيانات المستخدم';
    }
} catch (Exception $e) {
    $error = 'حدث خطأ في جلب البيانات';
}

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $bio = trim($_POST['bio']);
    
    // Validate input
    if (empty($full_name) || empty($email)) {
        $error = 'الاسم الكامل والبريد الإلكتروني مطلوبان';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } else {
        // Check if email is already taken by another user
        $existing_user = fetch("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $user_id]);
        if ($existing_user) {
            $error = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
        } else {
            try {
                // Update user data
                $sql = "UPDATE users SET full_name = ?, email = ?, bio = ?, updated_at = NOW() WHERE id = ?";
                $result = query($sql, [$full_name, $email, $bio, $user_id]);
                
                if ($result) {
                    $success = 'تم تحديث الملف الشخصي بنجاح';
                    $_SESSION['user_name'] = $full_name; // Update session
                    $user['full_name'] = $full_name;
                    $user['email'] = $email;
                    $user['bio'] = $bio;
                } else {
                    $error = 'حدث خطأ في تحديث البيانات';
                }
            } catch (Exception $e) {
                $error = 'حدث خطأ في تحديث البيانات';
            }
        }
    }
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validate input
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error = 'جميع حقول كلمة المرور مطلوبة';
    } elseif (strlen($new_password) < 6) {
        $error = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
    } elseif ($new_password !== $confirm_password) {
        $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقتان';
    } else {
        // Verify current password
        if (password_verify($current_password, $user['password'])) {
            try {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $sql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
                $result = query($sql, [$hashed_password, $user_id]);
                
                if ($result) {
                    $success = 'تم تغيير كلمة المرور بنجاح';
                } else {
                    $error = 'حدث خطأ في تغيير كلمة المرور';
                }
            } catch (Exception $e) {
                $error = 'حدث خطأ في تغيير كلمة المرور';
            }
        } else {
            $error = 'كلمة المرور الحالية غير صحيحة';
        }
    }
}

// Handle profile image upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_image']) && isset($_FILES['profile_image'])) {
    $upload_dir = '../uploads/profiles/';
    
    // Create directory if it doesn't exist
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    $file = $_FILES['profile_image'];
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $max_size = 5 * 1024 * 1024; // 5MB
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        if (in_array($file['type'], $allowed_types) && $file['size'] <= $max_size) {
            $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $new_filename = 'profile_' . $user_id . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;
            
            if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                try {
                    // Delete old profile image if exists
                    if ($user['profile_image'] && file_exists('../' . $user['profile_image'])) {
                        unlink('../' . $user['profile_image']);
                    }
                    
                    $relative_path = 'uploads/profiles/' . $new_filename;
                    $sql = "UPDATE users SET profile_image = ?, updated_at = NOW() WHERE id = ?";
                    $result = query($sql, [$relative_path, $user_id]);
                    
                    if ($result) {
                        $success = 'تم تحديث الصورة الشخصية بنجاح';
                        $user['profile_image'] = $relative_path;
                    } else {
                        $error = 'حدث خطأ في حفظ الصورة';
                    }
                } catch (Exception $e) {
                    $error = 'حدث خطأ في حفظ الصورة';
                }
            } else {
                $error = 'حدث خطأ في رفع الصورة';
            }
        } else {
            $error = 'نوع الملف غير مدعوم أو حجم الملف كبير جداً (الحد الأقصى 5MB)';
        }
    } else {
        $error = 'حدث خطأ في رفع الملف';
    }
}

// Get user statistics
try {
    $stats = [
        'total_courses' => 0,
        'completed_courses' => 0,
        'certificates' => 0,
        'total_hours' => 0
    ];
    
    // Get enrolled courses count
    $enrolled_count = fetch("SELECT COUNT(*) as count FROM enrollments WHERE user_id = ?", [$user_id]);
    $stats['total_courses'] = $enrolled_count ? $enrolled_count['count'] : 0;
    
    // Get completed courses count
    $completed_count = fetch("SELECT COUNT(*) as count FROM enrollments WHERE user_id = ? AND status = 'completed'", [$user_id]);
    $stats['completed_courses'] = $completed_count ? $completed_count['count'] : 0;
    
    // Get certificates count
    $cert_count = fetch("SELECT COUNT(*) as count FROM certificates WHERE user_id = ?", [$user_id]);
    $stats['certificates'] = $cert_count ? $cert_count['count'] : 0;
    
    // Get total study hours (approximate)
    $hours_result = fetch("
        SELECT SUM(c.duration) as total_minutes 
        FROM enrollments e 
        JOIN courses c ON e.course_id = c.id 
        WHERE e.user_id = ? AND e.status = 'completed'
    ", [$user_id]);
    $stats['total_hours'] = $hours_result ? round($hours_result['total_minutes'] / 60, 1) : 0;
    
} catch (Exception $e) {
    // Use default stats if database query fails
}

$page_title = 'الملف الشخصي';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
            --shadow-medium: 0 10px 30px rgba(0,0,0,0.1);
            --border-radius: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: var(--shadow-medium);
            padding: 1rem 0;
        }

        .profile-header {
            background: var(--primary-gradient);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .profile-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .profile-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 5px solid white;
            box-shadow: var(--shadow-medium);
            object-fit: cover;
            position: relative;
            z-index: 2;
        }

        .profile-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-medium);
            margin-bottom: 2rem;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .stats-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow-light);
            text-align: center;
            transition: var(--transition);
            border: none;
            height: 100%;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stats-icon.primary { background: var(--primary-gradient); }
        .stats-icon.success { background: var(--success-gradient); }
        .stats-icon.warning { background: var(--secondary-gradient); }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: var(--transition);
        }

        .btn-gradient:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            transition: var(--transition);
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .section-title {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 1.5rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.5rem;
        }

        .nav-tabs {
            border-bottom: 2px solid #e2e8f0;
            margin-bottom: 2rem;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 12px 12px 0 0;
            color: #718096;
            font-weight: 600;
            padding: 12px 24px;
            margin-left: 5px;
            transition: var(--transition);
        }

        .nav-tabs .nav-link.active {
            background: var(--primary-gradient);
            color: white;
        }

        .nav-tabs .nav-link:hover:not(.active) {
            background: #f7fafc;
            color: #667eea;
        }
    </style>
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand fw-bold" href="dashboard.php">
            <i class="fas fa-graduation-cap me-2"></i>
            منصة التعليم التفاعلي
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-home me-1"></i> الرئيسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="courses.php">
                        <i class="fas fa-book me-1"></i> دوراتي
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="certificates.php">
                        <i class="fas fa-certificate me-1"></i> الشهادات
                    </a>
                </li>
            </ul>

            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn btn-link text-white text-decoration-none dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <img src="<?php echo $user['profile_image'] ? '../' . $user['profile_image'] : '../assets/images/avatar-placeholder.jpg'; ?>"
                             class="rounded-circle me-2" width="32" height="32" alt="<?php echo htmlspecialchars($user['full_name']); ?>"
                             onerror="this.src='../assets/images/avatar-placeholder.jpg'">
                        <?php echo htmlspecialchars($user['full_name']); ?>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item active" href="profile.php">
                                <i class="fas fa-user me-2"></i> الملف الشخصي
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Profile Header -->
<div class="profile-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-3 text-center">
                <img src="<?php echo $user['profile_image'] ? '../' . $user['profile_image'] : '../assets/images/avatar-placeholder.jpg'; ?>"
                     class="profile-avatar" alt="<?php echo htmlspecialchars($user['full_name']); ?>"
                     onerror="this.src='../assets/images/avatar-placeholder.jpg'">
            </div>
            <div class="col-md-9">
                <h1 class="mb-2"><?php echo htmlspecialchars($user['full_name']); ?></h1>
                <p class="mb-2"><i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($user['email']); ?></p>
                <p class="mb-2"><i class="fas fa-calendar me-2"></i>عضو منذ <?php echo date('F Y', strtotime($user['created_at'])); ?></p>
                <?php if ($user['bio']): ?>
                    <p class="mb-0"><i class="fas fa-info-circle me-2"></i><?php echo htmlspecialchars($user['bio']); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Alerts -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-book"></i>
                </div>
                <div class="stats-number"><?php echo $stats['total_courses']; ?></div>
                <div class="text-muted">إجمالي الدورات</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-number"><?php echo $stats['completed_courses']; ?></div>
                <div class="text-muted">دورات مكتملة</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon warning">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="stats-number"><?php echo $stats['certificates']; ?></div>
                <div class="text-muted">الشهادات</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-number"><?php echo $stats['total_hours']; ?></div>
                <div class="text-muted">ساعات الدراسة</div>
            </div>
        </div>
    </div>

    <!-- Profile Management Tabs -->
    <div class="profile-card">
        <ul class="nav nav-tabs" id="profileTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab">
                    <i class="fas fa-user me-2"></i>البيانات الشخصية
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="password-tab" data-bs-toggle="tab" data-bs-target="#password" type="button" role="tab">
                    <i class="fas fa-lock me-2"></i>تغيير كلمة المرور
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="image-tab" data-bs-toggle="tab" data-bs-target="#image" type="button" role="tab">
                    <i class="fas fa-image me-2"></i>الصورة الشخصية
                </button>
            </li>
        </ul>

        <div class="tab-content" id="profileTabsContent">
            <!-- Profile Information Tab -->
            <div class="tab-pane fade show active" id="profile" role="tabpanel">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name"
                                   value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="bio" class="form-label">نبذة شخصية</label>
                        <textarea class="form-control" id="bio" name="bio" rows="4"
                                  placeholder="اكتب نبذة مختصرة عن نفسك..."><?php echo htmlspecialchars($user['bio'] ?? ''); ?></textarea>
                    </div>
                    <button type="submit" name="update_profile" class="btn btn-gradient">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                </form>
            </div>

            <!-- Password Change Tab -->
            <div class="tab-pane fade" id="password" role="tabpanel">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية *</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة *</label>
                            <input type="password" class="form-control" id="new_password" name="new_password"
                                   minlength="6" required>
                            <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                   minlength="6" required>
                        </div>
                    </div>
                    <button type="submit" name="change_password" class="btn btn-gradient">
                        <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                    </button>
                </form>
            </div>

            <!-- Profile Image Tab -->
            <div class="tab-pane fade" id="image" role="tabpanel">
                <div class="row">
                    <div class="col-md-4 text-center mb-3">
                        <img src="<?php echo $user['profile_image'] ? '../' . $user['profile_image'] : '../assets/images/avatar-placeholder.jpg'; ?>"
                             class="img-fluid rounded-circle" style="width: 200px; height: 200px; object-fit: cover;"
                             alt="الصورة الشخصية" id="preview-image"
                             onerror="this.src='../assets/images/avatar-placeholder.jpg'">
                    </div>
                    <div class="col-md-8">
                        <form method="POST" action="" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="profile_image" class="form-label">اختر صورة جديدة</label>
                                <input type="file" class="form-control" id="profile_image" name="profile_image"
                                       accept="image/jpeg,image/png,image/gif" onchange="previewImage(this)">
                                <div class="form-text">
                                    الأنواع المدعومة: JPG, PNG, GIF | الحد الأقصى: 5MB
                                </div>
                            </div>
                            <button type="submit" name="upload_image" class="btn btn-gradient">
                                <i class="fas fa-upload me-2"></i>رفع الصورة
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Password Recovery Section -->
    <div class="profile-card">
        <h3 class="section-title">استرجاع كلمة المرور</h3>
        <div class="row">
            <div class="col-md-8">
                <p class="text-muted mb-3">
                    في حالة نسيان كلمة المرور، يمكنك استخدام خاصية استرجاع كلمة المرور عبر البريد الإلكتروني.
                </p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>كيفية استرجاع كلمة المرور:</strong>
                    <ol class="mb-0 mt-2">
                        <li>اذهب إلى صفحة تسجيل الدخول</li>
                        <li>اضغط على "نسيت كلمة المرور؟"</li>
                        <li>أدخل بريدك الإلكتروني المسجل</li>
                        <li>ستصلك رسالة تحتوي على رابط إعادة تعيين كلمة المرور</li>
                        <li>اتبع التعليمات في الرسالة لإنشاء كلمة مرور جديدة</li>
                    </ol>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <a href="../forgot-password.php" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-key me-2"></i>استرجاع كلمة المرور
                </a>
            </div>
        </div>
    </div>

    <!-- Account Security Tips -->
    <div class="profile-card">
        <h3 class="section-title">نصائح أمان الحساب</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="d-flex align-items-start mb-3">
                    <div class="stats-icon primary me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div>
                        <h5>استخدم كلمة مرور قوية</h5>
                        <p class="text-muted mb-0">اختر كلمة مرور تحتوي على أحرف كبيرة وصغيرة وأرقام ورموز</p>
                    </div>
                </div>
                <div class="d-flex align-items-start mb-3">
                    <div class="stats-icon success me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div>
                        <h5>غير كلمة المرور بانتظام</h5>
                        <p class="text-muted mb-0">ننصح بتغيير كلمة المرور كل 3-6 أشهر</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-start mb-3">
                    <div class="stats-icon warning me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                        <i class="fas fa-eye-slash"></i>
                    </div>
                    <div>
                        <h5>لا تشارك بياناتك</h5>
                        <p class="text-muted mb-0">لا تشارك كلمة المرور أو بيانات الحساب مع أي شخص</p>
                    </div>
                </div>
                <div class="d-flex align-items-start mb-3">
                    <div class="stats-icon primary me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                        <i class="fas fa-envelope-open-text"></i>
                    </div>
                    <div>
                        <h5>تحقق من بريدك الإلكتروني</h5>
                        <p class="text-muted mb-0">تأكد من صحة بريدك الإلكتروني لاستقبال التنبيهات الأمنية</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Preview image before upload
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-image').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Password confirmation validation
document.addEventListener('DOMContentLoaded', function() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');

    function validatePassword() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('كلمة المرور غير متطابقة');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }

    if (newPassword && confirmPassword) {
        newPassword.addEventListener('input', validatePassword);
        confirmPassword.addEventListener('input', validatePassword);
    }

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

</body>
</html>
