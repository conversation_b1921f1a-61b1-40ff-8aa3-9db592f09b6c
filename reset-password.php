<?php
require_once 'config/config.php';
require_once 'config/database.php';

// Redirect if already logged in
if (isAuthenticated()) {
    redirect('student/dashboard.php');
}

$success = '';
$error = '';
$token = $_GET['token'] ?? '';

// Validate token
if (empty($token)) {
    $error = 'رابط إعادة التعيين غير صحيح';
} else {
    // Check if token exists and is valid
    $reset_request = fetch("
        SELECT * FROM password_resets 
        WHERE token = ? AND expires_at > NOW() AND used_at IS NULL
    ", [$token]);
    
    if (!$reset_request) {
        $error = 'رابط إعادة التعيين غير صحيح أو منتهي الصلاحية';
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$error) {
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validate input
    if (empty($new_password) || empty($confirm_password)) {
        $error = 'جميع الحقول مطلوبة';
    } elseif (strlen($new_password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($new_password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيدها غير متطابقتان';
    } else {
        try {
            // Update user password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET password = ?, updated_at = NOW() WHERE email = ?";
            $result = query($sql, [$hashed_password, $reset_request['email']]);
            
            if ($result) {
                // Mark token as used
                query("UPDATE password_resets SET used_at = NOW() WHERE token = ?", [$token]);
                
                $success = 'تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول';
            } else {
                $error = 'حدث خطأ في تحديث كلمة المرور';
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ في تحديث كلمة المرور';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-medium: 0 10px 30px rgba(0,0,0,0.1);
            --border-radius: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            color: #2d3748;
        }

        .auth-container {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            margin: 2rem auto;
        }

        .auth-header {
            background: var(--primary-gradient);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .auth-header i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .auth-body {
            padding: 2rem;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: var(--transition);
            width: 100%;
        }

        .btn-gradient:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.5rem;
        }

        .input-group-text {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-left: none;
            border-radius: 12px 0 0 12px;
        }

        .input-group .form-control {
            border-right: none;
            border-radius: 0 12px 12px 0;
        }

        .input-group .form-control:focus {
            border-right: none;
        }

        .back-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }

        .back-link:hover {
            color: #764ba2;
            text-decoration: none;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="auth-container">
        <div class="auth-header">
            <i class="fas fa-lock"></i>
            <h2 class="mb-0">إعادة تعيين كلمة المرور</h2>
            <p class="mb-0 mt-2">أدخل كلمة المرور الجديدة</p>
        </div>
        
        <div class="auth-body">
            <?php if ($success): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                </div>
                <div class="text-center">
                    <a href="login.php" class="btn btn-gradient">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                </div>
            <?php elseif ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                </div>
                <div class="text-center">
                    <a href="forgot-password.php" class="back-link">
                        <i class="fas fa-arrow-right me-2"></i>طلب رابط جديد
                    </a>
                </div>
            <?php else: ?>
                <form method="POST" action="" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="new_password" name="new_password" 
                                   placeholder="أدخل كلمة المرور الجديدة" minlength="6" required>
                        </div>
                        <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   placeholder="أعد إدخال كلمة المرور" minlength="6" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-gradient mb-3">
                        <i class="fas fa-save me-2"></i>حفظ كلمة المرور الجديدة
                    </button>
                </form>
                
                <div class="text-center">
                    <a href="login.php" class="back-link">
                        <i class="fas fa-arrow-right me-2"></i>العودة لتسجيل الدخول
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Password confirmation validation
document.addEventListener('DOMContentLoaded', function() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (newPassword && confirmPassword) {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                confirmPassword.setCustomValidity('');
            }
        }
    }
    
    if (newPassword && confirmPassword) {
        newPassword.addEventListener('input', validatePassword);
        confirmPassword.addEventListener('input', validatePassword);
    }
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

</body>
</html>
