/**
 * Courses Page JavaScript
 * Enhanced functionality for the courses page
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize all course page functionality
    initViewToggle();
    initSearchFunctionality();
    initFilterAnimations();
    initCourseCardInteractions();
    initImageHandling();
    initScrollEffects();
    initLoadingAnimations();
});

/**
 * View Toggle Functionality (Grid/List)
 */
function initViewToggle() {
    const viewButtons = document.querySelectorAll('[data-view]');
    const coursesGrid = document.getElementById('courses-grid');
    
    if (!coursesGrid) return;
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.dataset.view;
            
            // Update active button
            viewButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Update grid layout with animation
            coursesGrid.style.opacity = '0.5';
            
            setTimeout(() => {
                if (view === 'list') {
                    coursesGrid.classList.remove('row', 'g-4');
                    coursesGrid.classList.add('list-view');
                    
                    // Change card layout for list view
                    coursesGrid.querySelectorAll('.col-md-6, .col-xl-4').forEach(col => {
                        col.className = 'col-12 mb-3';
                        const card = col.querySelector('.course-card');
                        if (card) {
                            card.classList.add('list-card');
                        }
                    });
                } else {
                    coursesGrid.classList.add('row', 'g-4');
                    coursesGrid.classList.remove('list-view');
                    
                    // Restore grid layout
                    coursesGrid.querySelectorAll('.col-12').forEach(col => {
                        col.className = 'col-md-6 col-xl-4';
                        const card = col.querySelector('.course-card');
                        if (card) {
                            card.classList.remove('list-card');
                        }
                    });
                }
                
                coursesGrid.style.opacity = '1';
            }, 200);
        });
    });
}

/**
 * Enhanced Search Functionality
 */
function initSearchFunctionality() {
    const searchInput = document.querySelector('input[name="search"]');
    const submitBtn = document.querySelector('.btn[type="submit"]');
    
    if (!searchInput || !submitBtn) return;
    
    let searchTimeout;
    const originalBtnContent = submitBtn.innerHTML;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = this.value.trim();
        
        if (searchTerm.length >= 2) {
            // Show loading indicator
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            submitBtn.disabled = true;
            
            // Auto-submit after delay
            searchTimeout = setTimeout(() => {
                document.getElementById('filter-form').submit();
            }, 800);
        } else if (searchTerm.length === 0) {
            // Clear search
            submitBtn.innerHTML = originalBtnContent;
            submitBtn.disabled = false;
            
            searchTimeout = setTimeout(() => {
                document.getElementById('filter-form').submit();
            }, 500);
        } else {
            // Reset button
            submitBtn.innerHTML = originalBtnContent;
            submitBtn.disabled = false;
        }
    });
    
    // Handle form submission
    document.getElementById('filter-form').addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        submitBtn.disabled = true;
    });
}

/**
 * Filter Animations
 */
function initFilterAnimations() {
    const filterSelects = document.querySelectorAll('select[name]');
    
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            // Add loading overlay
            showLoadingOverlay('جاري تحديث النتائج...');
            
            // Submit form after short delay for better UX
            setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    });
}

/**
 * Course Card Interactions
 */
function initCourseCardInteractions() {
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        // Make entire card clickable
        card.addEventListener('click', function(e) {
            if (!e.target.closest('a, button')) {
                const courseLink = this.querySelector('.card-title a');
                if (courseLink) {
                    window.location.href = courseLink.href;
                }
            }
        });
        
        // Add bookmark functionality
        addBookmarkButton(card);
        
        // Enhanced hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

/**
 * Add Bookmark Button to Course Card
 */
function addBookmarkButton(card) {
    const bookmarkBtn = document.createElement('button');
    bookmarkBtn.className = 'btn btn-sm btn-outline-danger position-absolute top-0 end-0 m-2 bookmark-btn';
    bookmarkBtn.innerHTML = '<i class="far fa-heart"></i>';
    bookmarkBtn.style.zIndex = '10';
    bookmarkBtn.title = 'إضافة للمفضلة';
    
    bookmarkBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        toggleBookmark(this);
    });
    
    card.style.position = 'relative';
    card.appendChild(bookmarkBtn);
}

/**
 * Toggle Bookmark State
 */
function toggleBookmark(button) {
    const icon = button.querySelector('i');
    const isBookmarked = icon.classList.contains('fas');
    
    if (isBookmarked) {
        // Remove bookmark
        icon.classList.remove('fas');
        icon.classList.add('far');
        button.classList.remove('btn-danger');
        button.classList.add('btn-outline-danger');
        button.title = 'إضافة للمفضلة';
        
        showNotification('تم إزالة الدورة من المفضلة', 'info');
    } else {
        // Add bookmark
        icon.classList.remove('far');
        icon.classList.add('fas');
        button.classList.remove('btn-outline-danger');
        button.classList.add('btn-danger');
        button.title = 'إزالة من المفضلة';
        
        showNotification('تم إضافة الدورة للمفضلة', 'success');
    }
    
    // Add animation
    button.style.transform = 'scale(1.2)';
    setTimeout(() => {
        button.style.transform = 'scale(1)';
    }, 200);
}

/**
 * Image Handling and Lazy Loading
 */
function initImageHandling() {
    const courseImages = document.querySelectorAll('.course-card img');
    
    courseImages.forEach(img => {
        // Add loading placeholder
        img.style.background = 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)';
        img.style.backgroundSize = '200% 100%';
        
        // Handle successful load
        img.addEventListener('load', function() {
            this.style.opacity = '0';
            this.style.transition = 'opacity 0.3s ease';
            setTimeout(() => {
                this.style.opacity = '1';
                this.style.background = 'none';
            }, 100);
        });
        
        // Handle load errors
        img.addEventListener('error', function() {
            this.src = 'https://via.placeholder.com/400x250/667eea/ffffff?text=Course+Image';
            this.style.background = 'none';
        });
    });
}

/**
 * Scroll Effects
 */
function initScrollEffects() {
    // Navbar scroll effect
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0,0,0,0.15)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
        }
    });
    
    // Scroll to top button
    createScrollToTopButton();
}

/**
 * Create Scroll to Top Button
 */
function createScrollToTopButton() {
    const scrollBtn = document.createElement('button');
    scrollBtn.className = 'btn btn-primary position-fixed bottom-0 end-0 m-4 rounded-circle scroll-to-top';
    scrollBtn.style.width = '50px';
    scrollBtn.style.height = '50px';
    scrollBtn.style.display = 'none';
    scrollBtn.style.zIndex = '1000';
    scrollBtn.style.transition = 'all 0.3s ease';
    scrollBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    scrollBtn.title = 'العودة للأعلى';
    
    scrollBtn.addEventListener('click', function() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
        this.style.transform = 'scale(0.9)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });
    
    document.body.appendChild(scrollBtn);
    
    // Show/hide based on scroll position
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            scrollBtn.style.display = 'flex';
            scrollBtn.style.alignItems = 'center';
            scrollBtn.style.justifyContent = 'center';
        } else {
            scrollBtn.style.display = 'none';
        }
    });
}

/**
 * Loading Animations
 */
function initLoadingAnimations() {
    window.addEventListener('load', function() {
        const cards = document.querySelectorAll('.course-card');
        
        cards.forEach((card, index) => {
            // Initial state
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            
            // Animate in with stagger
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
}

/**
 * Utility Functions
 */

// Show loading overlay
function showLoadingOverlay(message = 'جاري التحميل...') {
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
    overlay.style.background = 'rgba(255,255,255,0.9)';
    overlay.style.zIndex = '9999';
    overlay.style.backdropFilter = 'blur(5px)';
    
    overlay.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;"></div>
            <p class="text-primary fw-semibold">${message}</p>
        </div>
    `;
    
    document.body.appendChild(overlay);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (overlay.parentElement) {
            overlay.remove();
        }
    }, 5000);
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} position-fixed top-0 end-0 m-3 notification-toast`;
    notification.style.zIndex = '10000';
    notification.style.minWidth = '300px';
    notification.style.transform = 'translateX(100%)';
    notification.style.transition = 'transform 0.3s ease';
    
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${getNotificationIcon(type)} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}

// Get notification icon
function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Reset filters function (global)
function resetFilters() {
    showLoadingOverlay('جاري إعادة تعيين الفلاتر...');
    setTimeout(() => {
        window.location.href = 'courses.php';
    }, 500);
}

// Load more courses function (global)
function loadMoreCourses() {
    showNotification('سيتم إضافة المزيد من الدورات قريباً!', 'info');
}
