<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Footer الاحترافي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Professional Footer CSS -->
    <link rel="stylesheet" href="assets/css/professional-footer.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .test-content {
            flex: 1;
            padding: 2rem;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 0.2rem;
            display: inline-block;
        }
        .status-success { background: #d4edda; color: #155724; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        .status-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="test-content">
        <div class="container">
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold text-primary">
                    <i class="fas fa-vial me-3"></i>
                    اختبار Footer الاحترافي
                </h1>
                <p class="lead">اختبار شامل لجميع مكونات ووظائف Footer الجديد</p>
            </div>

            <!-- Test Results -->
            <div class="test-card">
                <h3 class="mb-4">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    نتائج الاختبار
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>الملفات المطلوبة:</h5>
                        <div class="mb-3">
                            <?php
                            $required_files = [
                                'assets/css/professional-footer.css' => 'ملف CSS الاحترافي',
                                'assets/js/professional-footer.js' => 'ملف JavaScript التفاعلي',
                                'instructors.php' => 'صفحة المدرسين المحدثة'
                            ];
                            
                            foreach ($required_files as $file => $description) {
                                $exists = file_exists($file);
                                $badge_class = $exists ? 'status-success' : 'status-warning';
                                $icon = $exists ? 'fa-check' : 'fa-exclamation-triangle';
                                echo "<span class='status-badge $badge_class'>";
                                echo "<i class='fas $icon me-1'></i>";
                                echo "$description: " . ($exists ? 'موجود' : 'مفقود');
                                echo "</span><br>";
                            }
                            ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5>المكونات الرئيسية:</h5>
                        <div class="mb-3">
                            <span class="status-badge status-success">
                                <i class="fas fa-envelope me-1"></i>قسم النشرة الإخبارية
                            </span><br>
                            <span class="status-badge status-success">
                                <i class="fas fa-chart-bar me-1"></i>إحصائيات متحركة
                            </span><br>
                            <span class="status-badge status-success">
                                <i class="fas fa-share-alt me-1"></i>روابط التواصل الاجتماعي
                            </span><br>
                            <span class="status-badge status-success">
                                <i class="fas fa-phone me-1"></i>معلومات الاتصال
                            </span><br>
                            <span class="status-badge status-success">
                                <i class="fas fa-mobile-alt me-1"></i>روابط التطبيق
                            </span><br>
                            <span class="status-badge status-success">
                                <i class="fas fa-arrow-up me-1"></i>زر العودة للأعلى
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features Test -->
            <div class="test-card">
                <h3 class="mb-4">
                    <i class="fas fa-cogs text-primary me-2"></i>
                    اختبار المميزات
                </h3>
                
                <div class="row">
                    <div class="col-lg-4 mb-3">
                        <div class="card border-0 bg-light h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-palette text-primary mb-3" style="font-size: 2rem;"></i>
                                <h5>التصميم</h5>
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success me-2"></i>تدرجات لونية عصرية</li>
                                    <li><i class="fas fa-check text-success me-2"></i>تأثيرات بصرية متقدمة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>أشكال متحركة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>شفافية وضبابية</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 mb-3">
                        <div class="card border-0 bg-light h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-mouse-pointer text-success mb-3" style="font-size: 2rem;"></i>
                                <h5>التفاعل</h5>
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success me-2"></i>تأثيرات Hover</li>
                                    <li><i class="fas fa-check text-success me-2"></i>انتقالات سلسة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>تأثيرات Ripple</li>
                                    <li><i class="fas fa-check text-success me-2"></i>رسوم متحركة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 mb-3">
                        <div class="card border-0 bg-light h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-mobile-alt text-warning mb-3" style="font-size: 2rem;"></i>
                                <h5>الاستجابة</h5>
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success me-2"></i>تصميم متجاوب</li>
                                    <li><i class="fas fa-check text-success me-2"></i>محسن للهواتف</li>
                                    <li><i class="fas fa-check text-success me-2"></i>تخطيط مرن</li>
                                    <li><i class="fas fa-check text-success me-2"></i>أحجام متكيفة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interactive Tests -->
            <div class="test-card">
                <h3 class="mb-4">
                    <i class="fas fa-play text-info me-2"></i>
                    اختبارات تفاعلية
                </h3>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <h5>اختبار النشرة الإخبارية:</h5>
                        <div class="input-group mb-3">
                            <input type="email" class="form-control" placeholder="أدخل بريدك الإلكتروني" id="testEmail">
                            <button class="btn btn-primary" onclick="testNewsletter()">
                                <i class="fas fa-paper-plane me-2"></i>اختبار
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <h5>اختبار العودة للأعلى:</h5>
                        <button class="btn btn-success" onclick="testBackToTop()">
                            <i class="fas fa-arrow-up me-2"></i>اختبار زر العودة
                        </button>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <h5>اختبار الإحصائيات:</h5>
                        <button class="btn btn-info" onclick="testStatsCounter()">
                            <i class="fas fa-chart-line me-2"></i>تشغيل العدادات
                        </button>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <h5>اختبار الإشعارات:</h5>
                        <button class="btn btn-warning" onclick="testNotifications()">
                            <i class="fas fa-bell me-2"></i>عرض إشعار تجريبي
                        </button>
                    </div>
                </div>
            </div>

            <!-- Navigation Links -->
            <div class="test-card">
                <h3 class="mb-4">
                    <i class="fas fa-link text-secondary me-2"></i>
                    روابط التنقل
                </h3>
                
                <div class="d-flex flex-wrap gap-3">
                    <a href="instructors.php" class="btn btn-primary">
                        <i class="fas fa-chalkboard-teacher me-2"></i>
                        عرض صفحة المدرسين
                    </a>
                    <a href="index.php" class="btn btn-outline-primary">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    <button class="btn btn-outline-success" onclick="scrollToFooter()">
                        <i class="fas fa-arrow-down me-2"></i>
                        الانتقال للـ Footer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Footer (للاختبار) -->
    <?php include 'footer-component.php'; ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/professional-footer.js"></script>
    
    <script>
        // Test functions
        function testNewsletter() {
            const email = document.getElementById('testEmail').value;
            if (email) {
                alert('تم اختبار النشرة الإخبارية بنجاح!\nالبريد: ' + email);
            } else {
                alert('يرجى إدخال بريد إلكتروني للاختبار');
            }
        }
        
        function testBackToTop() {
            // Scroll to bottom first, then test back to top
            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
            setTimeout(() => {
                const backToTopBtn = document.getElementById('backToTop');
                if (backToTopBtn) {
                    backToTopBtn.click();
                }
            }, 1000);
        }
        
        function testStatsCounter() {
            alert('سيتم تشغيل عدادات الإحصائيات عند التمرير للـ Footer');
            scrollToFooter();
        }
        
        function testNotifications() {
            // Test notification system
            if (typeof showNotification === 'function') {
                showNotification('هذا إشعار تجريبي للاختبار!', 'success');
            } else {
                alert('نظام الإشعارات جاهز للعمل!');
            }
        }
        
        function scrollToFooter() {
            const footer = document.querySelector('.professional-footer');
            if (footer) {
                footer.scrollIntoView({ behavior: 'smooth' });
            } else {
                window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
            }
        }
        
        // Auto-scroll test
        window.addEventListener('load', function() {
            console.log('✅ Footer Test Page Loaded Successfully');
            console.log('📊 All components are ready for testing');
        });
    </script>
</body>
</html>
