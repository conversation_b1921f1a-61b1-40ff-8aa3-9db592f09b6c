<?php
// Setup database with additional tables and sample data
require_once 'config/database_auto.php';

try {
    echo "Setting up database...\n";
    
    // Read and execute SQL file
    $sql = file_get_contents('database/additional_tables.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (PDOException $e) {
                echo "⚠ Warning: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n✅ Database setup completed successfully!\n";
    echo "📊 Sample data has been inserted.\n";
    echo "🎯 You can now view the updated index.php page.\n";
    
} catch (Exception $e) {
    echo "❌ Error setting up database: " . $e->getMessage() . "\n";
}
?>
