<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة التعليم التفاعلي - تعلم بلا حدود</title>
    <meta name="description" content="منصة تعليمية متطورة تقدم أفضل الدورات التعليمية مع مدربين محترفين. ابدأ رحلتك التعليمية اليوم!">

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #56ab2f;
            --warning-color: #ff6b6b;
            --dark-color: #2d3748;
            --light-color: #f8fafc;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --gradient-success: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            --gradient-accent: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
            --shadow-xl: 0 20px 25px rgba(0,0,0,0.15);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            overflow-x: hidden;
        }

        /* Navbar Styles */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            padding: 1rem 0;
            transition: var(--transition);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: var(--shadow-lg);
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 600;
            color: var(--dark-color) !important;
            transition: var(--transition);
            position: relative;
            margin: 0 0.5rem;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 50%;
            background: var(--gradient-primary);
            transition: var(--transition);
            transform: translateX(-50%);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition);
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: var(--transition);
        }

        .btn-outline-primary:hover {
            background: var(--gradient-primary);
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding-top: 100px;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
            margin-top: 3rem;
        }

        .stat-item {
            text-align: center;
            color: white;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .hero-image {
            position: relative;
            z-index: 2;
        }

        .hero-image img {
            max-width: 100%;
            height: auto;
            filter: drop-shadow(0 20px 40px rgba(0,0,0,0.2));
        }

        /* Search Bar */
        .search-container {
            background: white;
            border-radius: 50px;
            padding: 0.5rem;
            box-shadow: var(--shadow-xl);
            margin-top: 2rem;
            max-width: 600px;
        }

        .search-input {
            border: none;
            outline: none;
            padding: 1rem 1.5rem;
            font-size: 1.1rem;
            width: 100%;
            background: transparent;
        }

        .search-btn {
            background: var(--gradient-primary);
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            transition: var(--transition);
        }

        .search-btn:hover {
            transform: scale(1.05);
        }

        /* Category Cards */
        .category-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(0,0,0,0.05);
            height: 100%;
            cursor: pointer;
        }

        .category-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        .category-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            transition: var(--transition);
        }

        .category-icon i {
            font-size: 2rem;
            color: white;
        }

        .category-card:hover .category-icon {
            transform: scale(1.1);
        }

        .category-card h4 {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--dark-color);
        }

        .category-card p {
            color: #6b7280;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .category-stats {
            font-size: 0.9rem;
            color: var(--primary-color);
            font-weight: 600;
        }

        /* Featured Courses */
        .course-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            height: 100%;
            border: none;
        }

        .course-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .course-image {
            position: relative;
            overflow: hidden;
            height: 200px;
        }

        .course-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .course-card:hover .course-image img {
            transform: scale(1.1);
        }

        .course-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--gradient-accent);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .course-content {
            padding: 1.5rem;
        }

        .course-category {
            color: var(--primary-color);
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .course-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0.5rem 0 1rem;
            color: var(--dark-color);
            line-height: 1.4;
        }

        .course-instructor {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .instructor-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-left: 0.75rem;
        }

        .instructor-name {
            font-size: 0.9rem;
            color: #6b7280;
        }

        .course-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .course-rating {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .rating-stars {
            color: #fbbf24;
        }

        .rating-number {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        .course-students {
            font-size: 0.9rem;
            color: #6b7280;
        }

        .course-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #e5e7eb;
        }

        .course-price {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--dark-color);
        }

        .course-price .original-price {
            font-size: 0.9rem;
            color: #9ca3af;
            text-decoration: line-through;
            margin-left: 0.5rem;
        }

        .course-duration {
            font-size: 0.9rem;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        /* Statistics */
        .stat-card {
            text-align: center;
            color: white;
        }

        .stat-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            transition: var(--transition);
        }

        .stat-icon i {
            font-size: 2rem;
            color: white;
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.3);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            color: white;
        }

        .stat-text {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }

        /* Testimonials */
        .testimonial-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            height: 100%;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }

        .testimonial-content {
            margin-bottom: 2rem;
        }

        .testimonial-stars {
            color: #fbbf24;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .testimonial-content p {
            font-style: italic;
            color: #4b5563;
            line-height: 1.7;
            margin: 0;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .testimonial-author img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
        }

        .author-info h5 {
            margin: 0;
            font-weight: 700;
            color: var(--dark-color);
        }

        .author-info span {
            color: #6b7280;
            font-size: 0.9rem;
        }

        /* Search Box */
        .search-box input {
            border: 2px solid #e5e7eb;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            transition: var(--transition);
        }

        .search-box input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .hero-stats {
                flex-wrap: wrap;
                gap: 1rem;
                justify-content: center;
            }

            .stat-item {
                min-width: 120px;
            }

            .search-container {
                flex-direction: column;
                gap: 1rem;
            }

            .search-btn {
                border-radius: 25px;
            }

            .category-card {
                padding: 1.5rem;
            }

            .course-card {
                margin-bottom: 2rem;
            }

            .stat-number {
                font-size: 2.5rem;
            }
        }

        /* Navbar Scroll Effect */
        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            backdrop-filter: blur(20px);
        }

        /* Loading Animation */
        .loading {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .loading.loaded {
            opacity: 1;
            transform: translateY(0);
        }

        /* Footer Styles */
        .footer {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            color: white;
        }

        .footer-title {
            color: #e2e8f0;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .footer-subtitle {
            color: #e2e8f0;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .footer-text {
            color: #cbd5e0;
            line-height: 1.7;
            margin-bottom: 2rem;
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links a {
            color: #cbd5e0;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .social-links {
            display: flex;
            gap: 1rem;
        }

        .social-link {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: var(--transition);
        }

        .social-link:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
        }

        .contact-info {
            margin-bottom: 2rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            color: #cbd5e0;
        }

        .contact-item i {
            width: 20px;
            margin-left: 1rem;
            color: var(--primary-color);
        }

        .newsletter-form {
            display: flex;
            border-radius: 25px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.1);
        }

        .newsletter-input {
            flex: 1;
            border: none;
            background: transparent;
            padding: 1rem 1.5rem;
            color: white;
            outline: none;
        }

        .newsletter-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .newsletter-btn {
            background: var(--primary-color);
            border: none;
            padding: 1rem 1.5rem;
            color: white;
            cursor: pointer;
            transition: var(--transition);
        }

        .newsletter-btn:hover {
            background: var(--secondary-color);
        }

        .footer-divider {
            border-color: rgba(255, 255, 255, 0.1);
            margin: 2rem 0;
        }

        .copyright {
            color: #cbd5e0;
            margin: 0;
        }

        .footer-bottom-links {
            display: flex;
            gap: 2rem;
            justify-content: end;
        }

        .footer-bottom-links a {
            color: #cbd5e0;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-bottom-links a:hover {
            color: #667eea;
        }

        @media (max-width: 768px) {
            .footer-bottom-links {
                justify-content: center;
                margin-top: 1rem;
                flex-wrap: wrap;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم التفاعلي
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            الدورات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses.php">جميع الدورات</a></li>
                            <li><a class="dropdown-item" href="courses.php?category=programming">البرمجة</a></li>
                            <li><a class="dropdown-item" href="courses.php?category=design">التصميم</a></li>
                            <li><a class="dropdown-item" href="courses.php?category=business">الأعمال</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="instructors.php">المدربين</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">اتصل بنا</a>
                    </li>
                </ul>

                <div class="d-flex align-items-center">
                    <div class="search-box me-3 d-none d-lg-block">
                        <input type="text" class="form-control" placeholder="ابحث عن دورة...">
                    </div>
                    <a href="login.php" class="btn btn-outline-primary me-2">تسجيل الدخول</a>
                    <a href="register.php" class="btn btn-primary">إنشاء حساب</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6 hero-content" data-aos="fade-right">
                    <h1 class="hero-title">
                        تعلم بلا حدود مع
                        <span class="d-block">أفضل المدربين</span>
                    </h1>
                    <p class="hero-subtitle">
                        اكتشف آلاف الدورات التعليمية المتميزة في البرمجة، التصميم، الأعمال وأكثر.
                        ابدأ رحلتك التعليمية اليوم واحصل على شهادات معتمدة.
                    </p>

                    <!-- Search Bar -->
                    <div class="search-container d-flex">
                        <input type="text" class="search-input" placeholder="ما الذي تريد تعلمه اليوم؟">
                        <button class="search-btn">
                            <i class="fas fa-search me-2"></i>ابحث
                        </button>
                    </div>

                    <!-- Hero Stats -->
                    <div class="hero-stats">
                        <div class="stat-item" data-aos="fade-up" data-aos-delay="100">
                            <span class="stat-number">10,000+</span>
                            <span class="stat-label">طالب نشط</span>
                        </div>
                        <div class="stat-item" data-aos="fade-up" data-aos-delay="200">
                            <span class="stat-number">500+</span>
                            <span class="stat-label">دورة تعليمية</span>
                        </div>
                        <div class="stat-item" data-aos="fade-up" data-aos-delay="300">
                            <span class="stat-number">100+</span>
                            <span class="stat-label">مدرب خبير</span>
                        </div>
                        <div class="stat-item" data-aos="fade-up" data-aos-delay="400">
                            <span class="stat-number">95%</span>
                            <span class="stat-label">معدل الرضا</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 hero-image" data-aos="fade-left">
                    <div class="position-relative">
                        <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                             alt="التعليم التفاعلي" class="img-fluid rounded-4">

                        <!-- Floating Cards -->
                        <div class="position-absolute top-0 start-0 bg-white p-3 rounded-3 shadow-lg"
                             style="transform: translate(-20px, 20px);" data-aos="zoom-in" data-aos-delay="500">
                            <div class="d-flex align-items-center">
                                <div class="bg-success rounded-circle p-2 me-2">
                                    <i class="fas fa-play text-white"></i>
                                </div>
                                <div>
                                    <small class="text-muted">دورة جديدة</small>
                                    <div class="fw-bold">تطوير المواقع</div>
                                </div>
                            </div>
                        </div>

                        <div class="position-absolute bottom-0 end-0 bg-white p-3 rounded-3 shadow-lg"
                             style="transform: translate(20px, -20px);" data-aos="zoom-in" data-aos-delay="600">
                            <div class="d-flex align-items-center">
                                <div class="bg-warning rounded-circle p-2 me-2">
                                    <i class="fas fa-star text-white"></i>
                                </div>
                                <div>
                                    <small class="text-muted">تقييم</small>
                                    <div class="fw-bold">4.9/5</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Categories -->
    <section class="categories py-5" style="background: var(--light-color);">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="display-5 fw-bold mb-3">استكشف الفئات الشائعة</h2>
                <p class="lead text-muted">اختر من بين مجموعة واسعة من المجالات التعليمية</p>
            </div>

            <div class="row g-4">
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h4>البرمجة وتطوير المواقع</h4>
                        <p>تعلم لغات البرمجة الحديثة وتطوير التطبيقات</p>
                        <div class="category-stats">
                            <span>120+ دورة</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <h4>التصميم والجرافيك</h4>
                        <p>إتقان فنون التصميم والإبداع البصري</p>
                        <div class="category-stats">
                            <span>85+ دورة</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4>إدارة الأعمال</h4>
                        <p>تطوير مهارات القيادة والإدارة الحديثة</p>
                        <div class="category-stats">
                            <span>95+ دورة</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <h4>التسويق الرقمي</h4>
                        <p>استراتيجيات التسويق الحديثة والمبيعات</p>
                        <div class="category-stats">
                            <span>70+ دورة</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="500">
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h4>تحليل البيانات</h4>
                        <p>تعلم علوم البيانات والذكاء الاصطناعي</p>
                        <div class="category-stats">
                            <span>60+ دورة</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="600">
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <h4>اللغات</h4>
                        <p>تعلم اللغات العالمية بطرق تفاعلية</p>
                        <div class="category-stats">
                            <span>45+ دورة</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="700">
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-camera"></i>
                        </div>
                        <h4>التصوير والمونتاج</h4>
                        <p>فنون التصوير وتحرير الفيديو الاحترافي</p>
                        <div class="category-stats">
                            <span>35+ دورة</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="800">
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-music"></i>
                        </div>
                        <h4>الموسيقى والفنون</h4>
                        <p>تعلم العزف والإنتاج الموسيقي</p>
                        <div class="category-stats">
                            <span>25+ دورة</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-5" data-aos="fade-up">
                <a href="courses.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-th-large me-2"></i>عرض جميع الفئات
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Courses -->
    <section class="featured-courses py-5">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="display-5 fw-bold mb-3">الدورات المميزة</h2>
                <p class="lead text-muted">اكتشف أفضل الدورات التعليمية المختارة بعناية</p>
            </div>

            <div class="row g-4">
                <!-- Course 1 -->
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="course-card">
                        <div class="course-image">
                            <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="دورة تطوير المواقع">
                            <div class="course-badge">الأكثر مبيعاً</div>
                        </div>
                        <div class="course-content">
                            <div class="course-category">البرمجة</div>
                            <h3 class="course-title">دورة تطوير المواقع الشاملة</h3>
                            <div class="course-instructor">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="المدرب" class="instructor-avatar">
                                <span class="instructor-name">أحمد محمد</span>
                            </div>
                            <div class="course-meta">
                                <div class="course-rating">
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="rating-number">4.9</span>
                                </div>
                                <span class="course-students">1,234 طالب</span>
                            </div>
                            <div class="course-footer">
                                <div class="course-price">
                                    299 ريال
                                    <span class="original-price">499 ريال</span>
                                </div>
                                <div class="course-duration">
                                    <i class="fas fa-clock"></i>
                                    12 ساعة
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course 2 -->
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="course-card">
                        <div class="course-image">
                            <img src="https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="دورة التصميم الجرافيكي">
                            <div class="course-badge">جديد</div>
                        </div>
                        <div class="course-content">
                            <div class="course-category">التصميم</div>
                            <h3 class="course-title">أساسيات التصميم الجرافيكي</h3>
                            <div class="course-instructor">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="المدربة" class="instructor-avatar">
                                <span class="instructor-name">فاطمة أحمد</span>
                            </div>
                            <div class="course-meta">
                                <div class="course-rating">
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                    </div>
                                    <span class="rating-number">4.7</span>
                                </div>
                                <span class="course-students">856 طالب</span>
                            </div>
                            <div class="course-footer">
                                <div class="course-price">199 ريال</div>
                                <div class="course-duration">
                                    <i class="fas fa-clock"></i>
                                    8 ساعات
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course 3 -->
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="course-card">
                        <div class="course-image">
                            <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="دورة التسويق الرقمي">
                            <div class="course-badge">مميز</div>
                        </div>
                        <div class="course-content">
                            <div class="course-category">التسويق</div>
                            <h3 class="course-title">استراتيجيات التسويق الرقمي</h3>
                            <div class="course-instructor">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="المدرب" class="instructor-avatar">
                                <span class="instructor-name">محمد علي</span>
                            </div>
                            <div class="course-meta">
                                <div class="course-rating">
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="rating-number">4.8</span>
                                </div>
                                <span class="course-students">2,156 طالب</span>
                            </div>
                            <div class="course-footer">
                                <div class="course-price">
                                    249 ريال
                                    <span class="original-price">399 ريال</span>
                                </div>
                                <div class="course-duration">
                                    <i class="fas fa-clock"></i>
                                    15 ساعة
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course 4 -->
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="course-card">
                        <div class="course-image">
                            <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="دورة تحليل البيانات">
                        </div>
                        <div class="course-content">
                            <div class="course-category">تحليل البيانات</div>
                            <h3 class="course-title">مقدمة في علوم البيانات</h3>
                            <div class="course-instructor">
                                <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="المدرب" class="instructor-avatar">
                                <span class="instructor-name">خالد السعيد</span>
                            </div>
                            <div class="course-meta">
                                <div class="course-rating">
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                    </div>
                                    <span class="rating-number">4.6</span>
                                </div>
                                <span class="course-students">743 طالب</span>
                            </div>
                            <div class="course-footer">
                                <div class="course-price">349 ريال</div>
                                <div class="course-duration">
                                    <i class="fas fa-clock"></i>
                                    20 ساعة
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course 5 -->
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
                    <div class="course-card">
                        <div class="course-image">
                            <img src="https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="دورة إدارة الأعمال">
                        </div>
                        <div class="course-content">
                            <div class="course-category">الأعمال</div>
                            <h3 class="course-title">أساسيات إدارة الأعمال</h3>
                            <div class="course-instructor">
                                <img src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="المدربة" class="instructor-avatar">
                                <span class="instructor-name">نورا حسن</span>
                            </div>
                            <div class="course-meta">
                                <div class="course-rating">
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                    </div>
                                    <span class="rating-number">4.7</span>
                                </div>
                                <span class="course-students">1,567 طالب</span>
                            </div>
                            <div class="course-footer">
                                <div class="course-price">مجاني</div>
                                <div class="course-duration">
                                    <i class="fas fa-clock"></i>
                                    6 ساعات
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course 6 -->
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
                    <div class="course-card">
                        <div class="course-image">
                            <img src="https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="دورة اللغة الإنجليزية">
                        </div>
                        <div class="course-content">
                            <div class="course-category">اللغات</div>
                            <h3 class="course-title">اللغة الإنجليزية للمبتدئين</h3>
                            <div class="course-instructor">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="المدرب" class="instructor-avatar">
                                <span class="instructor-name">يوسف أحمد</span>
                            </div>
                            <div class="course-meta">
                                <div class="course-rating">
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="rating-number">4.9</span>
                                </div>
                                <span class="course-students">3,421 طالب</span>
                            </div>
                            <div class="course-footer">
                                <div class="course-price">
                                    149 ريال
                                    <span class="original-price">249 ريال</span>
                                </div>
                                <div class="course-duration">
                                    <i class="fas fa-clock"></i>
                                    10 ساعات
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-5" data-aos="fade-up">
                <a href="courses.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-graduation-cap me-2"></i>عرض جميع الدورات
                </a>
            </div>
        </div>
    </section>

    <!-- Statistics -->
    <section class="stats py-5" style="background: var(--gradient-primary);">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="display-5 fw-bold text-white mb-3">أرقام تتحدث عن نجاحنا</h2>
                <p class="lead text-white opacity-75">انضم إلى آلاف الطلاب الذين حققوا أهدافهم معنا</p>
            </div>

            <div class="row text-center">
                <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="stat-number" data-count="15000">0</h3>
                        <p class="stat-text">طالب نشط</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <h3 class="stat-number" data-count="500">0</h3>
                        <p class="stat-text">دورة تعليمية</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h3 class="stat-number" data-count="150">0</h3>
                        <p class="stat-text">مدرب خبير</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <h3 class="stat-number" data-count="98">0</h3>
                        <p class="stat-text">% معدل الرضا</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="testimonials py-5" style="background: var(--light-color);">
        <div class="container">
            <div class="text-center mb-5" data-aos="fade-up">
                <h2 class="display-5 fw-bold mb-3">ماذا يقول طلابنا</h2>
                <p class="lead text-muted">تجارب حقيقية من طلاب حققوا النجاح معنا</p>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <div class="testimonial-stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <p>"منصة رائعة ساعدتني في تطوير مهاراتي في البرمجة. المحتوى عالي الجودة والمدربين محترفين جداً."</p>
                        </div>
                        <div class="testimonial-author">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="أحمد محمد">
                            <div class="author-info">
                                <h5>أحمد محمد</h5>
                                <span>مطور ويب</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <div class="testimonial-stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <p>"تعلمت التصميم الجرافيكي من الصفر وأصبحت الآن أعمل كمصممة مستقلة. شكراً لهذه المنصة الرائعة."</p>
                        </div>
                        <div class="testimonial-author">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="فاطمة أحمد">
                            <div class="author-info">
                                <h5>فاطمة أحمد</h5>
                                <span>مصممة جرافيك</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <div class="testimonial-stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <p>"دورات التسويق الرقمي غيرت مسار حياتي المهنية. المحتوى عملي ومفيد جداً للتطبيق الفوري."</p>
                        </div>
                        <div class="testimonial-author">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="محمد علي">
                            <div class="author-info">
                                <h5>محمد علي</h5>
                                <span>مختص تسويق رقمي</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container py-5">
            <div class="row">
                <!-- About Section -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="footer-title">
                        <i class="fas fa-graduation-cap me-2"></i>
                        منصة التعليم التفاعلي
                    </h5>
                    <p class="footer-text">
                        منصة تعليمية متطورة تهدف إلى تقديم أفضل تجربة تعليمية تفاعلية للطلاب في الوطن العربي
                        من خلال دورات عالية الجودة ومحتوى تعليمي متميز.
                    </p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="footer-subtitle">روابط سريعة</h6>
                    <ul class="footer-links">
                        <li><a href="index.php">الرئيسية</a></li>
                        <li><a href="courses.php">الدورات</a></li>
                        <li><a href="instructors.php">المدربين</a></li>
                        <li><a href="about.php">من نحن</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="footer-subtitle">الفئات</h6>
                    <ul class="footer-links">
                        <li><a href="courses.php?category=programming">البرمجة</a></li>
                        <li><a href="courses.php?category=design">التصميم</a></li>
                        <li><a href="courses.php?category=business">الأعمال</a></li>
                        <li><a href="courses.php?category=marketing">التسويق</a></li>
                        <li><a href="courses.php?category=languages">اللغات</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h6 class="footer-subtitle">معلومات التواصل</h6>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>+966 11 123 4567</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>الرياض، المملكة العربية السعودية</span>
                        </div>
                    </div>

                    <!-- Newsletter -->
                    <div class="newsletter mt-4">
                        <h6 class="footer-subtitle">اشترك في النشرة الإخبارية</h6>
                        <div class="newsletter-form">
                            <input type="email" class="newsletter-input" placeholder="بريدك الإلكتروني">
                            <button class="newsletter-btn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="footer-divider">

            <!-- Bottom Footer -->
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="copyright">&copy; 2024 منصة التعليم التفاعلي. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-bottom-links">
                        <a href="#">سياسة الخصوصية</a>
                        <a href="#">شروط الاستخدام</a>
                        <a href="#">ملفات تعريف الارتباط</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('mainNavbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Counter animation for statistics
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');

            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-count'));
                const duration = 2000; // 2 seconds
                const increment = target / (duration / 16); // 60fps
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current).toLocaleString();
                }, 16);
            });
        }

        // Trigger counter animation when stats section is visible
        const statsSection = document.querySelector('.stats');
        if (statsSection) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCounters();
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });

            observer.observe(statsSection);
        }

        // Search functionality
        const searchInput = document.querySelector('.search-input');
        const searchBtn = document.querySelector('.search-btn');

        if (searchInput && searchBtn) {
            searchBtn.addEventListener('click', function() {
                const query = searchInput.value.trim();
                if (query) {
                    window.location.href = `courses.php?search=${encodeURIComponent(query)}`;
                }
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchBtn.click();
                }
            });
        }

        // Category card click handlers
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                // Add click animation
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // Navigate to category (you can customize this)
                // window.location.href = 'courses.php?category=...';
            });
        });

        // Course card hover effects
        document.querySelectorAll('.course-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Newsletter subscription
        const newsletterForm = document.querySelector('.newsletter-form');
        if (newsletterForm) {
            const newsletterInput = newsletterForm.querySelector('.newsletter-input');
            const newsletterBtn = newsletterForm.querySelector('.newsletter-btn');

            newsletterBtn.addEventListener('click', function() {
                const email = newsletterInput.value.trim();
                if (email && isValidEmail(email)) {
                    // Simulate subscription
                    this.innerHTML = '<i class="fas fa-check"></i>';
                    this.style.background = '#10b981';

                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-paper-plane"></i>';
                        this.style.background = '';
                        newsletterInput.value = '';

                        // Show success message
                        showNotification('تم الاشتراك بنجاح!', 'success');
                    }, 2000);
                } else {
                    showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
                }
            });
        }

        // Email validation
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                <span>${message}</span>
            `;

            // Add notification styles
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                z-index: 9999;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Loading animation for page elements
        window.addEventListener('load', function() {
            document.querySelectorAll('.loading').forEach(element => {
                element.classList.add('loaded');
            });
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const heroSection = document.querySelector('.hero-section');
            if (heroSection) {
                heroSection.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Add loading class to elements that should animate on load
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.course-card, .category-card, .testimonial-card');
            animatedElements.forEach(element => {
                element.classList.add('loading');
            });
        });
    </script>
</body>
</html> 