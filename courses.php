<?php
require_once 'config/config.php';
require_once 'config/database.php';

// Get categories (with error handling)
try {
    $categories = fetchAll("SELECT * FROM categories ORDER BY name");
} catch (Exception $e) {
    $categories = [];
}

// Get filters with proper sanitization
$category_id = isset($_GET['category']) ? (int)$_GET['category'] : null;
$level = isset($_GET['level']) ? htmlspecialchars($_GET['level']) : '';
$price = isset($_GET['price']) ? htmlspecialchars($_GET['price']) : '';
$search = isset($_GET['search']) ? htmlspecialchars($_GET['search']) : '';

// Sample courses data with real images
$sample_courses = [
    [
        'id' => 1,
        'title' => 'تطوير المواقع باستخدام HTML و CSS',
        'description' => 'تعلم أساسيات تطوير المواقع من الصفر باستخدام HTML و CSS مع مشاريع عملية ومتقدمة',
        'thumbnail' => 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=250&fit=crop&crop=center',
        'price' => 0,
        'level' => 'beginner',
        'category_name' => 'تطوير الويب',
        'instructor_name' => 'أحمد محمد',
        'rating' => 4.8,
        'enrolled_students' => 150,
        'duration' => 20,
        'created_at' => date('Y-m-d H:i:s')
    ],
    [
        'id' => 2,
        'title' => 'البرمجة بلغة JavaScript',
        'description' => 'دورة شاملة لتعلم JavaScript من المبتدئ إلى المحترف مع التطبيقات العملية والمشاريع الحقيقية',
        'thumbnail' => 'https://images.unsplash.com/photo-1579468118864-1b9ea3c0db4a?w=400&h=250&fit=crop&crop=center',
        'price' => 299,
        'level' => 'intermediate',
        'category_name' => 'البرمجة',
        'instructor_name' => 'فاطمة علي',
        'rating' => 4.9,
        'enrolled_students' => 200,
        'duration' => 35,
        'created_at' => date('Y-m-d H:i:s')
    ],
    [
        'id' => 3,
        'title' => 'تصميم الجرافيك باستخدام Photoshop',
        'description' => 'تعلم تصميم الجرافيك الاحترافي باستخدام Adobe Photoshop مع مشاريع إبداعية ومتنوعة',
        'thumbnail' => 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=250&fit=crop&crop=center',
        'price' => 199,
        'level' => 'beginner',
        'category_name' => 'التصميم',
        'instructor_name' => 'محمد حسن',
        'rating' => 4.7,
        'enrolled_students' => 120,
        'duration' => 25,
        'created_at' => date('Y-m-d H:i:s')
    ],
    [
        'id' => 4,
        'title' => 'التسويق الرقمي ووسائل التواصل الاجتماعي',
        'description' => 'استراتيجيات التسويق الرقمي الحديثة وإدارة حملات وسائل التواصل الاجتماعي بفعالية',
        'thumbnail' => 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop&crop=center',
        'price' => 399,
        'level' => 'intermediate',
        'category_name' => 'التسويق',
        'instructor_name' => 'سارة أحمد',
        'rating' => 4.6,
        'enrolled_students' => 180,
        'duration' => 30,
        'created_at' => date('Y-m-d H:i:s')
    ],
    [
        'id' => 5,
        'title' => 'تطوير تطبيقات الهاتف المحمول',
        'description' => 'تعلم تطوير تطبيقات الهاتف المحمول باستخدام React Native و Flutter مع مشاريع عملية',
        'thumbnail' => 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=250&fit=crop&crop=center',
        'price' => 0,
        'level' => 'advanced',
        'category_name' => 'تطوير التطبيقات',
        'instructor_name' => 'عمر خالد',
        'rating' => 4.8,
        'enrolled_students' => 95,
        'duration' => 40,
        'created_at' => date('Y-m-d H:i:s')
    ],
    [
        'id' => 6,
        'title' => 'إدارة المشاريع الاحترافية',
        'description' => 'تعلم أساسيات وأدوات إدارة المشاريع الحديثة وتطبيقها في بيئة العمل الحقيقية',
        'thumbnail' => 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=400&h=250&fit=crop&crop=center',
        'price' => 249,
        'level' => 'intermediate',
        'category_name' => 'إدارة الأعمال',
        'instructor_name' => 'نور الدين',
        'rating' => 4.5,
        'enrolled_students' => 160,
        'duration' => 28,
        'created_at' => date('Y-m-d H:i:s')
    ],
    [
        'id' => 7,
        'title' => 'تعلم Python للمبتدئين',
        'description' => 'ابدأ رحلتك في عالم البرمجة مع لغة Python السهلة والقوية مع تطبيقات عملية',
        'thumbnail' => 'https://images.unsplash.com/photo-1526379095098-d400fd0bf935?w=400&h=250&fit=crop&crop=center',
        'price' => 0,
        'level' => 'beginner',
        'category_name' => 'البرمجة',
        'instructor_name' => 'خالد أحمد',
        'rating' => 4.9,
        'enrolled_students' => 220,
        'duration' => 30,
        'created_at' => date('Y-m-d H:i:s')
    ],
    [
        'id' => 8,
        'title' => 'تصميم المواقع باستخدام Figma',
        'description' => 'تعلم تصميم واجهات المستخدم الحديثة باستخدام Figma مع أفضل الممارسات',
        'thumbnail' => 'https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?w=400&h=250&fit=crop&crop=center',
        'price' => 179,
        'level' => 'intermediate',
        'category_name' => 'التصميم',
        'instructor_name' => 'مريم سالم',
        'rating' => 4.7,
        'enrolled_students' => 140,
        'duration' => 22,
        'created_at' => date('Y-m-d H:i:s')
    ],
    [
        'id' => 9,
        'title' => 'الذكاء الاصطناعي وتعلم الآلة',
        'description' => 'اكتشف عالم الذكاء الاصطناعي وتعلم الآلة مع تطبيقات عملية باستخدام Python',
        'thumbnail' => 'https://images.unsplash.com/photo-1555255707-c07966088b7b?w=400&h=250&fit=crop&crop=center',
        'price' => 499,
        'level' => 'advanced',
        'category_name' => 'الذكاء الاصطناعي',
        'instructor_name' => 'د. علي حسن',
        'rating' => 4.8,
        'enrolled_students' => 85,
        'duration' => 45,
        'created_at' => date('Y-m-d H:i:s')
    ]
];

// Try to get courses from database, fallback to sample data
try {
    $sql = "SELECT c.*, u.full_name as instructor_name, cat.name as category_name
            FROM courses c
            LEFT JOIN users u ON c.instructor_id = u.id
            LEFT JOIN categories cat ON c.category_id = cat.id
            WHERE c.status = 'published'";
    $params = [];

    if ($category_id) {
        $sql .= " AND c.category_id = ?";
        $params[] = $category_id;
    }

    if ($level) {
        $sql .= " AND c.level = ?";
        $params[] = $level;
    }

    if ($price) {
        switch ($price) {
            case 'free':
                $sql .= " AND c.price = 0";
                break;
            case 'paid':
                $sql .= " AND c.price > 0";
                break;
            case 'discount':
                $sql .= " AND c.discount_price IS NOT NULL";
                break;
        }
    }

    if ($search) {
        $sql .= " AND (c.title LIKE ? OR c.description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    $sql .= " ORDER BY c.created_at DESC";
    $courses = fetchAll($sql, $params);

    // If no courses found in database, use sample data
    if (empty($courses)) {
        $courses = $sample_courses;

        // Apply filters to sample data
        if ($level) {
            $courses = array_filter($courses, function($course) use ($level) {
                return $course['level'] === $level;
            });
        }

        if ($price) {
            $courses = array_filter($courses, function($course) use ($price) {
                switch ($price) {
                    case 'free':
                        return $course['price'] == 0;
                    case 'paid':
                        return $course['price'] > 0;
                    default:
                        return true;
                }
            });
        }

        if ($search) {
            $courses = array_filter($courses, function($course) use ($search) {
                return stripos($course['title'], $search) !== false ||
                       stripos($course['description'], $search) !== false;
            });
        }
    }
} catch (Exception $e) {
    $courses = $sample_courses;
}

// Sample categories if database is empty
if (empty($categories)) {
    $categories = [
        ['id' => 1, 'name' => 'تطوير الويب'],
        ['id' => 2, 'name' => 'البرمجة'],
        ['id' => 3, 'name' => 'التصميم'],
        ['id' => 4, 'name' => 'التسويق'],
        ['id' => 5, 'name' => 'تطوير التطبيقات'],
        ['id' => 6, 'name' => 'إدارة الأعمال'],
        ['id' => 7, 'name' => 'الذكاء الاصطناعي']
    ];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدورات التعليمية - <?php echo APP_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/courses.css">
    <link rel="stylesheet" href="assets/css/professional-footer.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                <?php echo APP_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item"><a class="nav-link" href="index.php">الرئيسية</a></li>
                    <li class="nav-item"><a class="nav-link active" href="courses.php">الدورات</a></li>
                    <li class="nav-item"><a class="nav-link" href="instructors.php">المدرسون</a></li>
                    <li class="nav-item"><a class="nav-link" href="about.php">من نحن</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">اتصل بنا</a></li>
                </ul>
                <div class="d-flex">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="dashboard.php" class="btn btn-outline-primary me-2">لوحة التحكم</a>
                        <a href="logout.php" class="btn btn-primary">تسجيل الخروج</a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-outline-primary me-2">تسجيل الدخول</a>
                        <a href="register.php" class="btn btn-primary">إنشاء حساب</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container hero-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="fas fa-book-open me-3"></i>
                        اكتشف دوراتنا التعليمية
                    </h1>
                    <p class="lead mb-4 fs-5">
                        مجموعة متنوعة من الدورات التعليمية عالية الجودة في مختلف المجالات،
                        مصممة لتناسب جميع المستويات من المبتدئ إلى المحترف
                    </p>
                    <div class="d-flex flex-wrap gap-3">
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-play-circle me-2"></i>
                            <?php echo count($courses); ?> دورة متاحة
                        </span>
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-users me-2"></i>
                            أكثر من 1000 طالب
                        </span>
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-certificate me-2"></i>
                            شهادات معتمدة
                        </span>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-laptop-code" style="font-size: 200px; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container py-5" style="margin-top: 80px;">
        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="card filters-card">
                    <div class="card-body">
                        <h5 class="filter-title">
                            <i class="fas fa-filter me-2"></i>
                            تصفية الدورات
                        </h5>

                        <form action="" method="GET" id="filter-form">
                            <!-- Search -->
                            <div class="mb-4">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-search me-2 text-primary"></i>
                                    البحث
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search"
                                           value="<?php echo htmlspecialchars($search); ?>"
                                           placeholder="ابحث عن دورة...">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Categories -->
                            <div class="mb-4">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-tags me-2 text-primary"></i>
                                    التصنيفات
                                </label>
                                <select class="form-select" name="category" onchange="this.form.submit()">
                                    <option value="">جميع التصنيفات</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"
                                                <?php echo $category_id == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Level -->
                            <div class="mb-4">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-layer-group me-2 text-primary"></i>
                                    المستوى
                                </label>
                                <select class="form-select" name="level" onchange="this.form.submit()">
                                    <option value="">جميع المستويات</option>
                                    <option value="beginner" <?php echo $level === 'beginner' ? 'selected' : ''; ?>>
                                        <i class="fas fa-seedling"></i> مبتدئ
                                    </option>
                                    <option value="intermediate" <?php echo $level === 'intermediate' ? 'selected' : ''; ?>>
                                        <i class="fas fa-chart-line"></i> متوسط
                                    </option>
                                    <option value="advanced" <?php echo $level === 'advanced' ? 'selected' : ''; ?>>
                                        <i class="fas fa-crown"></i> متقدم
                                    </option>
                                </select>
                            </div>

                            <!-- Price -->
                            <div class="mb-4">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-dollar-sign me-2 text-primary"></i>
                                    السعر
                                </label>
                                <select class="form-select" name="price" onchange="this.form.submit()">
                                    <option value="">جميع الأسعار</option>
                                    <option value="free" <?php echo $price === 'free' ? 'selected' : ''; ?>>
                                        <i class="fas fa-gift"></i> مجاني
                                    </option>
                                    <option value="paid" <?php echo $price === 'paid' ? 'selected' : ''; ?>>
                                        <i class="fas fa-credit-card"></i> مدفوع
                                    </option>
                                    <option value="discount" <?php echo $price === 'discount' ? 'selected' : ''; ?>>
                                        <i class="fas fa-percentage"></i> خصم
                                    </option>
                                </select>
                            </div>

                            <!-- Filter Summary -->
                            <?php if ($search || $category_id || $level || $price): ?>
                                <div class="alert alert-info">
                                    <small>
                                        <i class="fas fa-info-circle me-1"></i>
                                        تم تطبيق <?php
                                        $filter_count = 0;
                                        if ($search) $filter_count++;
                                        if ($category_id) $filter_count++;
                                        if ($level) $filter_count++;
                                        if ($price) $filter_count++;
                                        echo $filter_count;
                                        ?> فلتر
                                    </small>
                                </div>
                            <?php endif; ?>

                            <!-- Reset Filters -->
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين الفلاتر
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card mt-4" style="background: var(--primary-gradient); color: white;">
                    <div class="card-body text-center">
                        <h6 class="card-title">إحصائيات سريعة</h6>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="mb-2">
                                    <i class="fas fa-book fa-2x"></i>
                                </div>
                                <div class="fw-bold"><?php echo count($courses); ?></div>
                                <small>دورة</small>
                            </div>
                            <div class="col-4">
                                <div class="mb-2">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                                <div class="fw-bold">1000+</div>
                                <small>طالب</small>
                            </div>
                            <div class="col-4">
                                <div class="mb-2">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                                <div class="fw-bold">4.8</div>
                                <small>تقييم</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Courses Grid -->
            <div class="col-lg-9">
                <!-- Header with View Toggle -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="h3 mb-1 fw-bold">
                            <i class="fas fa-graduation-cap me-2 text-primary"></i>
                            الدورات المتاحة
                        </h2>
                        <p class="text-muted mb-0">
                            <?php echo count($courses); ?> دورة متاحة
                            <?php if ($search || $category_id || $level || $price): ?>
                                - مفلترة حسب اختيارك
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="view-toggle btn-group">
                        <button type="button" class="btn active" data-view="grid" title="عرض شبكي">
                            <i class="fas fa-th"></i>
                        </button>
                        <button type="button" class="btn" data-view="list" title="عرض قائمة">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- Courses Content -->
                <?php if (empty($courses)): ?>
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <h4 class="mt-3 mb-2">لم يتم العثور على دورات</h4>
                        <p class="text-muted mb-4">
                            لم نجد أي دورات تطابق معايير البحث الخاصة بك.
                            جرب تعديل الفلاتر أو البحث عن شيء آخر.
                        </p>
                        <button class="btn btn-primary" onclick="resetFilters()">
                            <i class="fas fa-undo me-2"></i>
                            إعادة تعيين البحث
                        </button>
                    </div>
                <?php else: ?>
                    <div class="row g-4" id="courses-grid">
                        <?php foreach ($courses as $course): ?>
                            <div class="col-md-6 col-xl-4">
                                <div class="card h-100 course-card">
                                    <!-- Course Image -->
                                    <div class="position-relative">
                                        <img src="<?php echo $course['thumbnail'] ?? 'https://via.placeholder.com/300x200/667eea/ffffff?text=Course'; ?>"
                                             class="card-img-top" alt="<?php echo htmlspecialchars($course['title']); ?>"
                                             onerror="this.src='https://via.placeholder.com/300x200/667eea/ffffff?text=Course'">

                                        <!-- Level Badge -->
                                        <span class="position-absolute top-0 start-0 m-3 badge bg-dark">
                                            <?php
                                            $level_text = [
                                                'beginner' => 'مبتدئ',
                                                'intermediate' => 'متوسط',
                                                'advanced' => 'متقدم'
                                            ];
                                            echo $level_text[$course['level']] ?? 'غير محدد';
                                            ?>
                                        </span>

                                        <!-- Price Badge -->
                                        <span class="position-absolute top-0 end-0 m-3 badge <?php echo $course['price'] > 0 ? 'bg-success' : 'bg-info'; ?>">
                                            <?php echo $course['price'] > 0 ? number_format($course['price']) . ' ج.م' : 'مجاني'; ?>
                                        </span>
                                    </div>

                                    <div class="card-body">
                                        <!-- Category -->
                                        <div class="mb-3">
                                            <span class="badge bg-primary">
                                                <i class="fas fa-tag me-1"></i>
                                                <?php echo htmlspecialchars($course['category_name']); ?>
                                            </span>
                                        </div>

                                        <!-- Title -->
                                        <h5 class="card-title">
                                            <a href="course.php?id=<?php echo $course['id']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($course['title']); ?>
                                            </a>
                                        </h5>

                                        <!-- Description -->
                                        <p class="card-text text-muted">
                                            <?php echo substr(strip_tags($course['description']), 0, 120) . '...'; ?>
                                        </p>

                                        <!-- Rating -->
                                        <div class="rating-stars mb-3">
                                            <?php
                                            $rating = $course['rating'] ?? 4.5;
                                            for ($i = 1; $i <= 5; $i++):
                                            ?>
                                                <i class="fas fa-star<?php echo $i <= $rating ? '' : ' text-muted'; ?>"></i>
                                            <?php endfor; ?>
                                            <span class="ms-2 text-muted">(<?php echo number_format($rating, 1); ?>)</span>
                                        </div>

                                        <!-- Instructor -->
                                        <div class="instructor-info mb-3">
                                            <img src="https://ui-avatars.com/api/?name=<?php echo urlencode($course['instructor_name']); ?>&background=667eea&color=fff&size=35"
                                                 class="instructor-avatar" alt="<?php echo htmlspecialchars($course['instructor_name']); ?>">
                                            <div>
                                                <div class="fw-semibold"><?php echo htmlspecialchars($course['instructor_name']); ?></div>
                                                <small class="text-muted">مدرس</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Course Stats -->
                                    <div class="course-stats">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="stat-item">
                                                    <i class="fas fa-users text-primary"></i>
                                                    <span><?php echo $course['enrolled_students'] ?? 0; ?></span>
                                                </div>
                                                <small class="text-muted">طالب</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="stat-item">
                                                    <i class="fas fa-clock text-warning"></i>
                                                    <span><?php echo $course['duration'] ?? 0; ?></span>
                                                </div>
                                                <small class="text-muted">ساعة</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="stat-item">
                                                    <i class="fas fa-play-circle text-success"></i>
                                                    <span><?php echo rand(10, 50); ?></span>
                                                </div>
                                                <small class="text-muted">درس</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Action Button -->
                                    <div class="card-footer bg-white border-0 pt-0">
                                        <a href="course.php?id=<?php echo $course['id']; ?>" class="btn btn-primary w-100">
                                            <i class="fas fa-play me-2"></i>
                                            <?php echo $course['price'] > 0 ? 'شراء الدورة' : 'ابدأ التعلم'; ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Load More Button -->
                    <?php if (count($courses) >= 6): ?>
                        <div class="text-center mt-5">
                            <button class="btn btn-outline-primary btn-lg" onclick="loadMoreCourses()">
                                <i class="fas fa-plus me-2"></i>
                                عرض المزيد من الدورات
                            </button>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Call to Action Section -->
    <section class="py-5" style="background: var(--primary-gradient); color: white;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-rocket me-2"></i>
                        ابدأ رحلتك التعليمية اليوم
                    </h3>
                    <p class="lead mb-0">
                        انضم إلى آلاف الطلاب الذين يطورون مهاراتهم معنا.
                        اختر الدورة المناسبة لك وابدأ التعلم الآن!
                    </p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="register.php" class="btn btn-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>
                        إنشاء حساب مجاني
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Professional Footer -->
    <footer class="professional-footer">
        <!-- Newsletter Section -->
        <div class="newsletter-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6 mb-4 mb-lg-0">
                        <div class="d-flex align-items-center">
                            <div class="newsletter-icon me-4">
                                <i class="fas fa-envelope-open"></i>
                            </div>
                            <div>
                                <h4 class="mb-2 text-white">اشترك في النشرة الإخبارية</h4>
                                <p class="mb-0 text-white-50">احصل على آخر الأخبار والدورات الجديدة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <form class="newsletter-form">
                            <div class="input-group">
                                <input type="email" class="form-control newsletter-input" placeholder="أدخل بريدك الإلكتروني">
                                <button class="btn btn-newsletter" type="submit">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    اشتراك
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="footer-main">
            <div class="container">
                <div class="row">
                    <!-- Company Info -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="footer-widget">
                            <div class="footer-logo mb-4">
                                <h3 class="text-white fw-bold">
                                    <i class="fas fa-graduation-cap me-2 text-primary"></i>
                                    <?php echo APP_NAME; ?>
                                </h3>
                            </div>
                            <p class="footer-desc mb-4">
                                منصة تعليمية تفاعلية رائدة تهدف إلى توفير أفضل تجربة تعلم عبر الإنترنت مع نخبة من أفضل المدرسين المحترفين في العالم العربي.
                            </p>

                            <!-- Stats -->
                            <div class="footer-stats mb-4">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h5 class="text-primary mb-1"><?php echo count($courses); ?>+</h5>
                                            <small class="text-white-50">دورة</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h5 class="text-primary mb-1">1000+</h5>
                                            <small class="text-white-50">طالب</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h5 class="text-primary mb-1">25+</h5>
                                            <small class="text-white-50">مدرس</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Social Media -->
                            <div class="social-links">
                                <h6 class="text-white mb-3">تابعنا على</h6>
                                <div class="d-flex gap-3">
                                    <a href="#" class="social-link facebook" title="Facebook">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                    <a href="#" class="social-link twitter" title="Twitter">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                    <a href="#" class="social-link instagram" title="Instagram">
                                        <i class="fab fa-instagram"></i>
                                    </a>
                                    <a href="#" class="social-link linkedin" title="LinkedIn">
                                        <i class="fab fa-linkedin-in"></i>
                                    </a>
                                    <a href="#" class="social-link youtube" title="YouTube">
                                        <i class="fab fa-youtube"></i>
                                    </a>
                                    <a href="#" class="social-link telegram" title="Telegram">
                                        <i class="fab fa-telegram-plane"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Course Categories -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">التصنيفات</h5>
                            <ul class="footer-links">
                                <?php foreach (array_slice($categories, 0, 6) as $category): ?>
                                    <li><a href="courses.php?category=<?php echo $category['id']; ?>">
                                        <i class="fas fa-tag me-2"></i><?php echo $category['name']; ?>
                                    </a></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">روابط سريعة</h5>
                            <ul class="footer-links">
                                <li><a href="index.php"><i class="fas fa-home me-2"></i>الرئيسية</a></li>
                                <li><a href="courses.php"><i class="fas fa-book me-2"></i>الدورات</a></li>
                                <li><a href="instructors.php"><i class="fas fa-chalkboard-teacher me-2"></i>المدرسون</a></li>
                                <li><a href="about.php"><i class="fas fa-info-circle me-2"></i>من نحن</a></li>
                                <li><a href="contact.php"><i class="fas fa-envelope me-2"></i>اتصل بنا</a></li>
                                <li><a href="blog.php"><i class="fas fa-blog me-2"></i>المدونة</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Contact & Support -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">تواصل معنا</h5>

                            <!-- Contact Info -->
                            <div class="contact-info mb-4">
                                <div class="contact-item mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="contact-icon me-3">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div>
                                            <h6 class="text-white mb-1">البريد الإلكتروني</h6>
                                            <a href="mailto:<EMAIL>" class="text-white-50"><EMAIL></a>
                                        </div>
                                    </div>
                                </div>

                                <div class="contact-item mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="contact-icon me-3">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <div>
                                            <h6 class="text-white mb-1">الهاتف</h6>
                                            <a href="tel:+201234567890" class="text-white-50">+20 ************</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="contact-item mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="contact-icon me-3">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <div>
                                            <h6 class="text-white mb-1">العنوان</h6>
                                            <span class="text-white-50">القاهرة، مصر</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="contact-item">
                                    <div class="d-flex align-items-center">
                                        <div class="contact-icon me-3">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div>
                                            <h6 class="text-white mb-1">ساعات العمل</h6>
                                            <span class="text-white-50">24/7 دعم متواصل</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Popular Courses -->
                            <div class="popular-courses">
                                <h6 class="text-white mb-3">الدورات الشائعة</h6>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php foreach (array_slice($courses, 0, 3) as $course): ?>
                                        <a href="course.php?id=<?php echo $course['id']; ?>"
                                           class="badge bg-primary text-decoration-none"
                                           title="<?php echo htmlspecialchars($course['title']); ?>">
                                            <?php echo substr($course['title'], 0, 20) . '...'; ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6 mb-3 mb-lg-0">
                        <div class="d-flex align-items-center">
                            <p class="mb-0 text-white-50">
                                &copy; 2024 <?php echo APP_NAME; ?>. جميع الحقوق محفوظة.
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="d-flex justify-content-lg-end justify-content-center flex-wrap gap-4">
                            <a href="terms.php" class="footer-bottom-link">الشروط والأحكام</a>
                            <a href="privacy.php" class="footer-bottom-link">سياسة الخصوصية</a>
                            <a href="cookies.php" class="footer-bottom-link">سياسة الكوكيز</a>
                            <a href="sitemap.php" class="footer-bottom-link">خريطة الموقع</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back to Top Button -->
        <button class="back-to-top" id="backToTop" title="العودة للأعلى">
            <i class="fas fa-chevron-up"></i>
        </button>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Courses Page JavaScript -->
    <script src="assets/js/courses.js"></script>

    <!-- Professional Footer JavaScript -->
    <script src="assets/js/professional-footer.js"></script>
        // Global functions for inline usage
        function resetFilters() {
            window.location.href = 'courses.php';
        }

        function loadMoreCourses() {
            alert('سيتم إضافة المزيد من الدورات قريباً!');
        }
    </script>
</body>
</html>