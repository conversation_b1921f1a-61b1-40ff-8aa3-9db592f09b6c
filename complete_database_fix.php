<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح شامل لاتصالات قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .fix-container {
            max-width: 1000px;
            margin: 30px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .fix-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .fix-content {
            padding: 2rem;
        }
        .status-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 0.5rem;
            border-radius: 5px;
            margin: 0.25rem 0;
        }
        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 0.5rem;
            border-radius: 5px;
            margin: 0.25rem 0;
        }
        .status-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 0.5rem;
            border-radius: 5px;
            margin: 0.25rem 0;
        }
        .status-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 0.5rem;
            border-radius: 5px;
            margin: 0.25rem 0;
        }
        .file-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="fix-header">
            <h1><i class="fas fa-tools me-3"></i>إصلاح شامل لاتصالات قاعدة البيانات</h1>
            <p class="mb-0">فحص وإصلاح جميع ملفات المشروع</p>
        </div>
        
        <div class="fix-content">
            <?php
            echo "<h4><i class='fas fa-search me-2'></i>البحث عن الملفات...</h4>";
            
            // Function to scan directory recursively
            function scanDirectory($dir, $extension = '.php') {
                $files = [];
                if (is_dir($dir)) {
                    $iterator = new RecursiveIteratorIterator(
                        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
                    );
                    
                    foreach ($iterator as $file) {
                        if ($file->isFile() && pathinfo($file, PATHINFO_EXTENSION) === ltrim($extension, '.')) {
                            $files[] = $file->getPathname();
                        }
                    }
                }
                return $files;
            }
            
            // Get all PHP files
            $all_php_files = scanDirectory('.');
            
            // Filter files that need database connection
            $files_needing_fix = [];
            $files_already_fixed = [];
            $files_with_errors = [];
            
            foreach ($all_php_files as $file) {
                // Skip certain files
                if (strpos($file, 'vendor/') !== false || 
                    strpos($file, 'node_modules/') !== false ||
                    strpos($file, '.git/') !== false ||
                    basename($file) === 'complete_database_fix.php' ||
                    basename($file) === 'fix_database_connections.php') {
                    continue;
                }
                
                try {
                    $content = file_get_contents($file);
                    
                    // Check if file uses database functions or includes database.php
                    if (preg_match('/require_once.*config\/database\.php/', $content) ||
                        preg_match('/fetchAll|fetch\(|insert\(|update\(|delete\(/', $content)) {
                        
                        if (strpos($content, 'config/database_auto.php') !== false) {
                            $files_already_fixed[] = $file;
                        } else {
                            $files_needing_fix[] = $file;
                        }
                    }
                } catch (Exception $e) {
                    $files_with_errors[] = ['file' => $file, 'error' => $e->getMessage()];
                }
            }
            
            echo "<div class='row'>";
            echo "<div class='col-md-4'>";
            echo "<div class='status-error'>";
            echo "<strong><i class='fas fa-exclamation-triangle me-2'></i>يحتاج إصلاح: " . count($files_needing_fix) . "</strong>";
            echo "</div>";
            echo "</div>";
            
            echo "<div class='col-md-4'>";
            echo "<div class='status-success'>";
            echo "<strong><i class='fas fa-check-circle me-2'></i>تم إصلاحه: " . count($files_already_fixed) . "</strong>";
            echo "</div>";
            echo "</div>";
            
            echo "<div class='col-md-4'>";
            echo "<div class='status-warning'>";
            echo "<strong><i class='fas fa-exclamation-circle me-2'></i>أخطاء: " . count($files_with_errors) . "</strong>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            
            // Fix files that need fixing
            if (!empty($files_needing_fix)) {
                echo "<h4 class='mt-4'><i class='fas fa-wrench me-2'></i>إصلاح الملفات...</h4>";
                echo "<div class='file-list'>";
                
                $fixed_count = 0;
                foreach ($files_needing_fix as $file) {
                    try {
                        $content = file_get_contents($file);
                        $original_content = $content;
                        
                        // Replace all variations of database.php includes
                        $replacements = [
                            "require_once 'config/database.php';" => "require_once 'config/database_auto.php';",
                            "require_once '../config/database.php';" => "require_once '../config/database_auto.php';",
                            "require_once '../../config/database.php';" => "require_once '../../config/database_auto.php';",
                            'require_once "config/database.php";' => 'require_once "config/database_auto.php";',
                            'require_once "../config/database.php";' => 'require_once "../config/database_auto.php";',
                            'require_once "../../config/database.php";' => 'require_once "../../config/database_auto.php";',
                        ];
                        
                        foreach ($replacements as $old => $new) {
                            $content = str_replace($old, $new, $content);
                        }
                        
                        if ($content !== $original_content) {
                            file_put_contents($file, $content);
                            echo "<div class='status-success'><i class='fas fa-check me-2'></i>" . str_replace('./', '', $file) . "</div>";
                            $fixed_count++;
                        } else {
                            echo "<div class='status-info'><i class='fas fa-info me-2'></i>" . str_replace('./', '', $file) . " (لا يحتاج تغيير)</div>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='status-error'><i class='fas fa-times me-2'></i>" . str_replace('./', '', $file) . " - خطأ: " . $e->getMessage() . "</div>";
                    }
                }
                
                echo "</div>";
                echo "<div class='status-success mt-3'>";
                echo "<strong><i class='fas fa-check-circle me-2'></i>تم إصلاح $fixed_count ملف بنجاح!</strong>";
                echo "</div>";
            }
            
            // Show already fixed files
            if (!empty($files_already_fixed)) {
                echo "<h4 class='mt-4'><i class='fas fa-check-circle me-2'></i>الملفات المُصلحة مسبقاً:</h4>";
                echo "<div class='file-list'>";
                foreach ($files_already_fixed as $file) {
                    echo "<div class='status-success'><i class='fas fa-check me-2'></i>" . str_replace('./', '', $file) . "</div>";
                }
                echo "</div>";
            }
            
            // Show files with errors
            if (!empty($files_with_errors)) {
                echo "<h4 class='mt-4'><i class='fas fa-exclamation-triangle me-2'></i>ملفات بها أخطاء:</h4>";
                echo "<div class='file-list'>";
                foreach ($files_with_errors as $error_info) {
                    echo "<div class='status-error'><i class='fas fa-times me-2'></i>" . str_replace('./', '', $error_info['file']) . " - " . $error_info['error'] . "</div>";
                }
                echo "</div>";
            }
            
            // Test database connection
            echo "<h4 class='mt-4'><i class='fas fa-database me-2'></i>اختبار الاتصال بقاعدة البيانات:</h4>";
            try {
                require_once 'config/database_auto.php';
                echo "<div class='status-success'>";
                echo "<i class='fas fa-check-circle me-2'></i><strong>نجح الاتصال بقاعدة البيانات!</strong><br>";
                echo "المضيف: " . DB_HOST . "<br>";
                echo "المستخدم: " . DB_USER . "<br>";
                echo "قاعدة البيانات: " . DB_NAME;
                echo "</div>";
                
                // Test some basic queries
                $test_queries = [
                    "SELECT 1 as test" => "اختبار أساسي",
                    "SHOW TABLES" => "عرض الجداول",
                    "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = '" . DB_NAME . "'" => "عدد الجداول"
                ];
                
                echo "<h5 class='mt-3'>اختبار الاستعلامات:</h5>";
                foreach ($test_queries as $query => $description) {
                    try {
                        $result = fetch($query);
                        echo "<div class='status-success'><i class='fas fa-check me-2'></i>$description - نجح</div>";
                    } catch (Exception $e) {
                        echo "<div class='status-error'><i class='fas fa-times me-2'></i>$description - فشل: " . $e->getMessage() . "</div>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<div class='status-error'>";
                echo "<i class='fas fa-times-circle me-2'></i><strong>فشل الاتصال بقاعدة البيانات:</strong><br>";
                echo $e->getMessage();
                echo "</div>";
            }
            ?>
            
            <div class="mt-4 text-center">
                <h4><i class="fas fa-rocket me-2"></i>الخطوات التالية:</h4>
                <div class="row">
                    <div class="col-md-3">
                        <a href="test_connection.php" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-database me-2"></i>اختبار الاتصال
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="database_setup.html" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-cog me-2"></i>إعداد قاعدة البيانات
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="index.php" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="instructor/dashboard.php" class="btn btn-outline-warning w-100 mb-2">
                            <i class="fas fa-chalkboard-teacher me-2"></i>لوحة المدرب
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
