<?php
// Test script to check registration functionality
require_once 'config/config.php';
require_once 'config/database_auto.php';

echo "<h2>اختبار وظيفة التسجيل</h2>";

// Test database connection
try {
    $test_query = $conn->query("SELECT 1");
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات يعمل بشكل صحيح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

// Test if users table exists
try {
    $table_check = $conn->query("DESCRIBE users");
    echo "<p style='color: green;'>✓ جدول المستخدمين موجود</p>";
    
    // Show table structure
    echo "<h3>هيكل جدول المستخدمين:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    while ($row = $table_check->fetch()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ جدول المستخدمين غير موجود: " . $e->getMessage() . "</p>";
    echo "<p>يرجى تشغيل ملف database/schema.sql لإنشاء الجداول</p>";
    exit;
}

// Test username generation function
function generateUsername($email) {
    global $conn;
    
    // Generate username from email (part before @)
    $username = strtolower(explode('@', $email)[0]);
    
    // Check if username already exists and make it unique if needed
    $original_username = $username;
    $counter = 1;
    while (fetch("SELECT id FROM users WHERE username = ?", [$username])) {
        $username = $original_username . $counter;
        $counter++;
    }
    
    return $username;
}

// Test username generation
$test_emails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
];

echo "<h3>اختبار توليد أسماء المستخدمين:</h3>";
foreach ($test_emails as $email) {
    $username = generateUsername($email);
    echo "<p>البريد الإلكتروني: <strong>$email</strong> → اسم المستخدم: <strong>$username</strong></p>";
}

// Test insert function
echo "<h3>اختبار دالة الإدراج:</h3>";
$test_data = [
    'username' => 'test_user_' . time(),
    'full_name' => 'مستخدم تجريبي',
    'email' => 'test_' . time() . '@example.com',
    'password' => password_hash('testpassword123', PASSWORD_DEFAULT),
    'role' => 'student'
];

$result = insert('users', $test_data);
if ($result) {
    echo "<p style='color: green;'>✓ تم إدراج المستخدم التجريبي بنجاح (ID: $result)</p>";
    
    // Clean up - delete test user
    delete('users', 'id = ?', [$result]);
    echo "<p style='color: blue;'>ℹ تم حذف المستخدم التجريبي</p>";
} else {
    echo "<p style='color: red;'>✗ فشل في إدراج المستخدم التجريبي</p>";
    global $conn;
    if (isset($conn) && $conn->errorInfo()[2]) {
        echo "<p style='color: red;'>تفاصيل الخطأ: " . $conn->errorInfo()[2] . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='register.php'>الذهاب إلى صفحة التسجيل</a></p>";
?>
