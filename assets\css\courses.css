/* Courses Page Styles */

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --card-shadow: 0 10px 30px rgba(0,0,0,0.1);
    --hover-shadow: 0 20px 40px rgba(0,0,0,0.15);
    --border-radius: 20px;
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Hero Section */
.hero-section {
    background: var(--primary-gradient);
    color: white;
    padding: 4rem 0 2rem;
    position: relative;
    overflow: hidden;
    margin-top: 76px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    transition: var(--transition);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #667eea !important;
}

.nav-link.active {
    color: #667eea !important;
    font-weight: 600;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* Filters Card */
.filters-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: none;
    position: sticky;
    top: 100px;
    transition: var(--transition);
}

.filters-card:hover {
    box-shadow: var(--hover-shadow);
}

.filters-card .card-body {
    padding: 2rem;
}

.filter-title {
    color: #333;
    font-weight: 700;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.filter-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: 15px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
    border-radius: 15px;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
    padding: 0.75rem 1.5rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    transform: translateY(-2px);
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* Course Cards */
.course-card {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: var(--card-shadow);
    background: white;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--hover-shadow);
}

.course-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-card:hover .card-img-top {
    transform: scale(1.05);
}

.course-card .card-body {
    padding: 1.5rem;
    flex-grow: 1;
}

.course-card .card-title {
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.4;
    font-size: 1.1rem;
}

.course-card .card-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.course-card .card-title a:hover {
    color: #667eea;
}

.course-card .card-text {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Badges */
.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.badge.bg-primary {
    background: var(--primary-gradient) !important;
}

.badge.bg-success {
    background: var(--success-gradient) !important;
}

.badge.bg-info {
    background: var(--secondary-gradient) !important;
}

.badge.bg-dark {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
}

/* Instructor Info */
.instructor-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

.instructor-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: 2px solid #e9ecef;
    object-fit: cover;
    transition: var(--transition);
}

.instructor-info:hover .instructor-avatar {
    border-color: #667eea;
    transform: scale(1.1);
}

/* Rating Stars */
.rating-stars {
    color: #ffc107;
    font-size: 0.9rem;
}

.rating-stars .fa-star.text-muted {
    color: #dee2e6 !important;
}

/* Course Stats */
.course-stats {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 1rem;
    margin-top: auto;
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 600;
}

.stat-item i {
    font-size: 1rem;
}

/* View Toggle */
.view-toggle .btn {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    background: white;
    color: #6c757d;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-toggle .btn.active {
    background: var(--primary-gradient);
    border-color: transparent;
    color: white;
    transform: scale(1.1);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.empty-state i {
    font-size: 4rem;
    color: #e9ecef;
    margin-bottom: 1rem;
}

.empty-state h4 {
    color: #495057;
    font-weight: 700;
}

.empty-state p {
    color: #6c757d;
    max-width: 400px;
    margin: 0 auto;
}

/* List View Styles */
.list-view .course-card {
    display: flex;
    flex-direction: row;
    height: auto;
}

.list-view .course-card .card-img-top {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
}

.list-view .course-card .card-body {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* Quick Stats Card */
.quick-stats-card {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
}

.quick-stats-card .card-title {
    font-size: 1rem;
    margin-bottom: 1rem;
}

.quick-stats-card .row > div {
    padding: 0.5rem;
}

.quick-stats-card i {
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.quick-stats-card .fw-bold {
    font-size: 1.2rem;
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0 1rem;
        text-align: center;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .filters-card {
        position: static;
        margin-bottom: 2rem;
    }
    
    .course-card .card-img-top {
        height: 180px;
    }
    
    .view-toggle {
        margin-top: 1rem;
    }
    
    .list-view .course-card {
        flex-direction: column;
    }
    
    .list-view .course-card .card-img-top {
        width: 100%;
        height: 180px;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 1.5rem 0;
    }
    
    .filters-card .card-body {
        padding: 1.5rem;
    }
    
    .course-card .card-body {
        padding: 1rem;
    }
    
    .course-stats {
        padding: 0.75rem;
    }
}

/* Loading Animation */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 10px;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}
