<?php
require_once 'config/config.php';
require_once 'config/database.php';

// Redirect if already logged in
if (isAuthenticated()) {
    redirect('student/dashboard.php');
}

$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    
    if (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } else {
        // Check if user exists
        $user = fetch("SELECT * FROM users WHERE email = ?", [$email]);
        
        if ($user) {
            // Generate reset token
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            try {
                // Store reset token in database (you might need to create this table)
                $sql = "INSERT INTO password_resets (email, token, expires_at, created_at) VALUES (?, ?, ?, NOW()) 
                        ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = NOW()";
                query($sql, [$email, $token, $expires]);
                
                // In a real application, you would send an email here
                // For now, we'll just show a success message
                $success = 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني';
                
                // You can uncomment and modify this section to actually send emails
                /*
                $reset_link = "http://yoursite.com/reset-password.php?token=" . $token;
                $subject = "إعادة تعيين كلمة المرور - منصة التعليم التفاعلي";
                $message = "مرحباً " . $user['full_name'] . ",\n\n";
                $message .= "لقد طلبت إعادة تعيين كلمة المرور الخاصة بك.\n";
                $message .= "يرجى النقر على الرابط التالي لإعادة تعيين كلمة المرور:\n\n";
                $message .= $reset_link . "\n\n";
                $message .= "هذا الرابط صالح لمدة ساعة واحدة فقط.\n";
                $message .= "إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذه الرسالة.\n\n";
                $message .= "شكراً لك،\nفريق منصة التعليم التفاعلي";
                
                mail($email, $subject, $message);
                */
                
            } catch (Exception $e) {
                $error = 'حدث خطأ في إرسال رابط إعادة التعيين';
            }
        } else {
            // Don't reveal if email exists or not for security
            $success = 'إذا كان البريد الإلكتروني مسجل لدينا، ستصلك رسالة إعادة تعيين كلمة المرور';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استرجاع كلمة المرور - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-medium: 0 10px 30px rgba(0,0,0,0.1);
            --border-radius: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            color: #2d3748;
        }

        .auth-container {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            margin: 2rem auto;
        }

        .auth-header {
            background: var(--primary-gradient);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .auth-header i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .auth-body {
            padding: 2rem;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: var(--transition);
            width: 100%;
        }

        .btn-gradient:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.5rem;
        }

        .input-group-text {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-left: none;
            border-radius: 12px 0 0 12px;
        }

        .input-group .form-control {
            border-right: none;
            border-radius: 0 12px 12px 0;
        }

        .input-group .form-control:focus {
            border-right: none;
        }

        .back-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }

        .back-link:hover {
            color: #764ba2;
            text-decoration: none;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="auth-container">
        <div class="auth-header">
            <i class="fas fa-key"></i>
            <h2 class="mb-0">استرجاع كلمة المرور</h2>
            <p class="mb-0 mt-2">أدخل بريدك الإلكتروني لإرسال رابط إعادة التعيين</p>
        </div>
        
        <div class="auth-body">
            <?php if ($success): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                </div>
                <div class="text-center">
                    <a href="login.php" class="back-link">
                        <i class="fas fa-arrow-right me-2"></i>العودة لتسجيل الدخول
                    </a>
                </div>
            <?php else: ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="أدخل بريدك الإلكتروني" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-gradient mb-3">
                        <i class="fas fa-paper-plane me-2"></i>إرسال رابط الاسترجاع
                    </button>
                </form>
                
                <div class="text-center">
                    <a href="login.php" class="back-link">
                        <i class="fas fa-arrow-right me-2"></i>العودة لتسجيل الدخول
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

</body>
</html>
