<?php
// Test script for instructors page
require_once 'config/config.php';
require_once 'config/database_auto.php';

echo "<h2>اختبار صفحة المدرسين</h2>";

// Test database connection
try {
    $test_query = $conn->query("SELECT 1");
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات يعمل</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// Test fetchAll function
echo "<h3>اختبار دالة fetchAll:</h3>";
try {
    $test_instructors = fetchAll("SELECT 1 as test");
    echo "<p style='color: green;'>✓ دالة fetchAll تعمل بشكل صحيح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في دالة fetchAll: " . $e->getMessage() . "</p>";
}

// Test users table
echo "<h3>اختبار جدول المستخدمين:</h3>";
try {
    $users_count = fetch("SELECT COUNT(*) as count FROM users");
    echo "<p style='color: green;'>✓ جدول المستخدمين موجود ويحتوي على " . $users_count['count'] . " مستخدم</p>";
    
    // Check for instructors
    $instructors_count = fetch("SELECT COUNT(*) as count FROM users WHERE role = 'instructor'");
    echo "<p style='color: blue;'>ℹ عدد المدرسين: " . $instructors_count['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في جدول المستخدمين: " . $e->getMessage() . "</p>";
}

// Test courses table
echo "<h3>اختبار جدول الدورات:</h3>";
try {
    $courses_count = fetch("SELECT COUNT(*) as count FROM courses");
    echo "<p style='color: green;'>✓ جدول الدورات موجود ويحتوي على " . $courses_count['count'] . " دورة</p>";
} catch (Exception $e) {
    echo "<p style='color: orange;'>⚠ جدول الدورات غير موجود أو فارغ: " . $e->getMessage() . "</p>";
}

// Test enrollments table
echo "<h3>اختبار جدول التسجيلات:</h3>";
try {
    $enrollments_count = fetch("SELECT COUNT(*) as count FROM enrollments");
    echo "<p style='color: green;'>✓ جدول التسجيلات موجود ويحتوي على " . $enrollments_count['count'] . " تسجيل</p>";
} catch (Exception $e) {
    echo "<p style='color: orange;'>⚠ جدول التسجيلات غير موجود أو فارغ: " . $e->getMessage() . "</p>";
}

// Test the actual query from instructors.php
echo "<h3>اختبار استعلام المدرسين:</h3>";
try {
    $instructors = fetchAll("
        SELECT 
            u.*,
            COUNT(DISTINCT c.id) as total_courses,
            COUNT(DISTINCT e.id) as total_students,
            AVG(c.rating) as average_rating
        FROM users u
        LEFT JOIN courses c ON u.id = c.instructor_id
        LEFT JOIN enrollments e ON c.id = e.course_id
        WHERE u.role = 'instructor'
        GROUP BY u.id
        ORDER BY total_courses DESC
    ");
    
    if (empty($instructors)) {
        echo "<p style='color: orange;'>⚠ لا يوجد مدرسون في قاعدة البيانات - سيتم استخدام بيانات تجريبية</p>";
    } else {
        echo "<p style='color: green;'>✓ تم العثور على " . count($instructors) . " مدرس</p>";
        foreach ($instructors as $instructor) {
            echo "<p>- " . $instructor['full_name'] . " (دورات: " . $instructor['total_courses'] . ", طلاب: " . $instructor['total_students'] . ")</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في استعلام المدرسين: " . $e->getMessage() . "</p>";
}

// Test file existence
echo "<h3>اختبار وجود الملفات:</h3>";
$files_to_check = [
    'instructors.php',
    'assets/css/instructors.css',
    'config/config.php',
    'config/database.php'
];

foreach ($files_to_check as $file) {
    $exists = file_exists($file);
    $status = $exists ? '✓' : '✗';
    $color = $exists ? 'green' : 'red';
    echo "<p style='color: $color;'>$status $file</p>";
}

echo "<hr>";
echo "<h3>روابط الاختبار:</h3>";
echo "<p><a href='instructors.php' target='_blank'>فتح صفحة المدرسين</a></p>";
echo "<p><a href='index.php'>العودة للرئيسية</a></p>";
?>
