<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شهاداتي - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
        
        .certificate-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .certificate-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .certificate-header {
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            color: #2d3748;
            padding: 2rem;
            text-align: center;
        }
        
        .certificate-icon {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: #ffd700;
        }
        
        .grade-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .skill-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin: 2px;
            display: inline-block;
        }
        
        .btn-download {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-download:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        /* Certificate Modal Styles */
        .certificate-modal {
            background: rgba(0, 0, 0, 0.9);
        }

        .certificate-preview {
            width: 210mm;
            height: 297mm;
            max-width: 100%;
            max-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            transform-origin: center center;
        }

        /* Auto-scale for different screen sizes */
        @media (max-width: 1200px) {
            .certificate-preview {
                transform: scale(0.8);
            }
        }

        @media (max-width: 900px) {
            .certificate-preview {
                transform: scale(0.6);
            }
        }

        @media (max-width: 600px) {
            .certificate-preview {
                transform: scale(0.4);
            }
        }

        .certificate-bg-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            background-size: 300px 300px, 400px 400px, 200px 200px;
        }

        .certificate-border {
            position: absolute;
            top: 15mm;
            left: 15mm;
            right: 15mm;
            bottom: 15mm;
            border: 6px solid rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .certificate-inner-border {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 3px solid #667eea;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .certificate-content {
            position: relative;
            height: 100%;
            padding: 25px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
            color: #2d3748;
        }

        .certificate-header-section {
            margin-bottom: 20px;
        }

        .certificate-logo {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            position: relative;
        }

        .certificate-logo::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 3px solid #ffd700;
            border-radius: 50%;
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .certificate-title {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .certificate-subtitle {
            font-size: 1rem;
            color: #718096;
            margin-bottom: 25px;
        }

        .certificate-body-section {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .certificate-recipient {
            font-size: 1.2rem;
            color: #4a5568;
            margin-bottom: 15px;
        }

        .certificate-name {
            font-size: 2.8rem;
            font-weight: 700;
            color: #2d3748;
            margin: 15px 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            line-height: 1.2;
        }

        .certificate-name::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        .certificate-course {
            font-size: 1.6rem;
            font-weight: 600;
            color: #667eea;
            margin: 20px 0;
            line-height: 1.3;
        }

        .certificate-description {
            font-size: 1rem;
            color: #4a5568;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .certificate-footer-section {
            display: flex;
            justify-content: space-between;
            align-items: end;
            margin-top: 40px;
        }

        .certificate-signature {
            text-align: center;
            min-width: 200px;
        }

        .signature-line {
            border-top: 3px solid #2d3748;
            margin-bottom: 10px;
            position: relative;
        }

        .signature-line::before {
            content: '';
            position: absolute;
            top: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 12px;
            background: #667eea;
            border-radius: 6px;
        }

        .signature-name {
            font-weight: 700;
            font-size: 1.1rem;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .signature-title {
            font-size: 0.9rem;
            color: #718096;
        }

        .certificate-grade-section {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            min-width: 150px;
        }

        .certificate-grade {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .certificate-grade-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .certificate-date-section {
            text-align: center;
            min-width: 200px;
        }

        .certificate-date {
            font-weight: 700;
            font-size: 1.1rem;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .certificate-date-label {
            font-size: 0.9rem;
            color: #718096;
        }

        .certificate-number {
            text-align: center;
            font-size: 0.8rem;
            color: #718096;
            background: rgba(102, 126, 234, 0.1);
            padding: 8px 15px;
            border-radius: 15px;
            border: 1px solid #e2e8f0;
            margin-top: 15px;
        }

        .certificate-decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .decorative-corner {
            position: absolute;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            opacity: 0.3;
        }

        .decorative-corner.top-left {
            top: 20px;
            left: 20px;
            border-radius: 0 0 50px 0;
        }

        .decorative-corner.top-right {
            top: 20px;
            right: 20px;
            border-radius: 0 0 0 50px;
        }

        .decorative-corner.bottom-left {
            bottom: 20px;
            left: 20px;
            border-radius: 0 50px 0 0;
        }

        .decorative-corner.bottom-right {
            bottom: 20px;
            right: 20px;
            border-radius: 50px 0 0 0;
        }

        /* Print Styles */
        @media print {
            body * {
                visibility: hidden;
            }

            .certificate-print-area,
            .certificate-print-area * {
                visibility: visible;
            }

            .certificate-print-area {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
            }

            .certificate-preview {
                width: 100% !important;
                height: 100% !important;
                max-width: none !important;
                max-height: none !important;
                margin: 0 !important;
                box-shadow: none !important;
                border-radius: 0 !important;
                page-break-inside: avoid;
                transform: none !important;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .certificate-bg-pattern {
                background-image:
                    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 80% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 40% 60%, rgba(102, 126, 234, 0.05) 0%, transparent 50%) !important;
            }

            .certificate-border {
                border: 6px solid #667eea !important;
                background: white !important;
                top: 10mm !important;
                left: 10mm !important;
                right: 10mm !important;
                bottom: 10mm !important;
            }

            .certificate-inner-border {
                border: 3px solid #667eea !important;
                background: white !important;
            }

            .certificate-logo::before {
                animation: none !important;
            }

            /* Portrait orientation */
            @page {
                size: A4 portrait;
                margin: 0;
            }

            /* Landscape orientation support */
            @page :first {
                size: A4 portrait;
            }

            .certificate-landscape {
                transform: rotate(90deg);
                transform-origin: center center;
                width: 297mm !important;
                height: 210mm !important;
            }
        }

        /* Landscape print styles */
        @media print and (orientation: landscape) {
            @page {
                size: A4 landscape;
                margin: 0;
            }

            .certificate-preview {
                width: 297mm !important;
                height: 210mm !important;
            }

            .certificate-border {
                top: 8mm !important;
                left: 15mm !important;
                right: 15mm !important;
                bottom: 8mm !important;
            }

            .certificate-content {
                padding: 20px !important;
            }

            .certificate-name {
                font-size: 2.5rem !important;
            }

            .certificate-course {
                font-size: 1.4rem !important;
            }
        }

        .print-hidden {
            display: block;
        }

        @media print {
            .print-hidden {
                display: none !important;
            }
        }
    </style>
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <a class="navbar-brand fw-bold" href="../index.php">
            <i class="fas fa-graduation-cap me-2"></i>
            منصة التعليم التفاعلي
        </a>
        
        <div class="navbar-nav me-auto">
            <a class="nav-link" href="dashboard.php">
                <i class="fas fa-tachometer-alt me-1"></i>
                لوحة التحكم
            </a>
            <a class="nav-link active" href="certificates.php">
                <i class="fas fa-certificate me-1"></i>
                الشهادات
            </a>
        </div>
    </div>
</nav>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-award me-3"></i>
                    شهاداتي
                </h1>
                <p class="lead mb-4">
                    عرض جميع الشهادات التي حصلت عليها من إكمال الدورات التدريبية
                </p>
                <div class="d-flex gap-3">
                    <div class="bg-white bg-opacity-20 rounded-pill px-4 py-2">
                        <i class="fas fa-certificate me-2"></i>
                        2 شهادة مكتملة
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-pill px-4 py-2">
                        <i class="fas fa-clock me-2"></i>
                        1 قيد التقدم
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-trophy" style="font-size: 6rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Certificates -->
<div class="container my-5">
    <h2 class="fw-bold mb-4">الشهادات المكتملة</h2>
    
    <div class="row g-4">
        <!-- Certificate 1 -->
        <div class="col-lg-6">
            <div class="certificate-card position-relative">
                <div class="grade-badge">95%</div>
                
                <div class="certificate-header">
                    <div class="certificate-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <h5 class="fw-bold mb-2">شهادة إتمام</h5>
                    <p class="mb-0">معتمدة من منصة التعليم التفاعلي</p>
                </div>
                
                <div class="p-4">
                    <h6 class="fw-bold mb-3">تطوير المواقع باستخدام HTML و CSS</h6>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted d-block">المدرس</small>
                            <div class="fw-medium">أحمد محمد</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">تاريخ الإكمال</small>
                            <div class="fw-medium">15 يناير 2024</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted d-block mb-2">المهارات المكتسبة</small>
                        <span class="skill-tag">HTML5</span>
                        <span class="skill-tag">CSS3</span>
                        <span class="skill-tag">Responsive Design</span>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn-download" onclick="downloadCertificate(1)">
                            <i class="fas fa-download me-2"></i>
                            تحميل الشهادة
                        </button>
                        <div class="row g-2">
                            <div class="col-6">
                                <button class="btn btn-outline-primary w-100" onclick="viewCertificate(1)">
                                    <i class="fas fa-eye me-2"></i>
                                    عرض
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-success w-100" onclick="printCertificateDirectly(1)">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Certificate 2 -->
        <div class="col-lg-6">
            <div class="certificate-card position-relative">
                <div class="grade-badge">88%</div>
                
                <div class="certificate-header">
                    <div class="certificate-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <h5 class="fw-bold mb-2">شهادة إتمام</h5>
                    <p class="mb-0">معتمدة من منصة التعليم التفاعلي</p>
                </div>
                
                <div class="p-4">
                    <h6 class="fw-bold mb-3">تصميم واجهات المستخدم UI/UX</h6>
                    
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted d-block">المدرس</small>
                            <div class="fw-medium">سارة أحمد</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">تاريخ الإكمال</small>
                            <div class="fw-medium">20 فبراير 2024</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted d-block mb-2">المهارات المكتسبة</small>
                        <span class="skill-tag">UI Design</span>
                        <span class="skill-tag">UX Research</span>
                        <span class="skill-tag">Figma</span>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn-download" onclick="downloadCertificate(2)">
                            <i class="fas fa-download me-2"></i>
                            تحميل الشهادة
                        </button>
                        <div class="row g-2">
                            <div class="col-6">
                                <button class="btn btn-outline-primary w-100" onclick="viewCertificate(2)">
                                    <i class="fas fa-eye me-2"></i>
                                    عرض
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-success w-100" onclick="printCertificateDirectly(2)">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Certificate Modal -->
<div class="modal fade certificate-modal" id="certificateModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header border-0 position-absolute" style="top: 20px; right: 20px; z-index: 1000;">
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-light rounded-circle" onclick="printCertificate()">
                        <i class="fas fa-print"></i>
                    </button>
                    <button type="button" class="btn btn-light rounded-circle" onclick="downloadCertificatePDF()">
                        <i class="fas fa-download"></i>
                    </button>
                    <button type="button" class="btn btn-light rounded-circle" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="modal-body d-flex align-items-center justify-content-center p-4">
                <div id="certificatePreview" class="certificate-preview">
                    <!-- Certificate content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Spacer before footer -->
<div style="height: 80px;"></div>

<!-- Footer -->
<footer style="background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%); color: white; padding: 4rem 0 2rem;">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-4 mb-4">
                <div class="mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-graduation-cap me-2" style="color: #667eea;"></i>
                        منصة التعليم التفاعلي
                    </h5>
                    <p class="text-light opacity-75 mb-4">منصة تعليمية متقدمة تهدف إلى توفير أفضل تجربة تعلم تفاعلية للطلاب في العالم العربي مع أحدث التقنيات التعليمية.</p>
                </div>
                <div class="d-flex gap-3">
                    <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;">
                        <i class="fab fa-facebook"></i>
                    </a>
                    <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;">
                        <i class="fab fa-linkedin"></i>
                    </a>
                </div>
            </div>

            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="fw-bold mb-3" style="color: #667eea;">روابط سريعة</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <a href="../index.php" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="../courses.php" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            الدورات
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="dashboard.php" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="certificates-simple.php" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            الشهادات
                        </a>
                    </li>
                </ul>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <h6 class="fw-bold mb-3" style="color: #667eea;">الدعم والمساعدة</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <a href="#help" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            مركز المساعدة
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#faq" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            الأسئلة الشائعة
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#privacy" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            سياسة الخصوصية
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#terms" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            شروط الاستخدام
                        </a>
                    </li>
                </ul>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <h6 class="fw-bold mb-3" style="color: #667eea;">تواصل معنا</h6>
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-primary bg-opacity-25 rounded-circle p-2 me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-envelope" style="color: #667eea;"></i>
                        </div>
                        <div>
                            <small class="text-light opacity-75">البريد الإلكتروني</small>
                            <div class="text-light"><EMAIL></div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-primary bg-opacity-25 rounded-circle p-2 me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-phone" style="color: #667eea;"></i>
                        </div>
                        <div>
                            <small class="text-light opacity-75">الهاتف</small>
                            <div class="text-light">+966 123 456 789</div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="bg-primary bg-opacity-25 rounded-circle p-2 me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-map-marker-alt" style="color: #667eea;"></i>
                        </div>
                        <div>
                            <small class="text-light opacity-75">العنوان</small>
                            <div class="text-light">الرياض، السعودية</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <hr class="my-4 opacity-25">

        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 opacity-75">&copy; 2024 منصة التعليم التفاعلي. جميع الحقوق محفوظة.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-0 opacity-75">
                    صُنع بـ <i class="fas fa-heart text-danger"></i> في المملكة العربية السعودية
                </p>
            </div>
        </div>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Certificate data
const certificatesData = {
    1: {
        studentName: 'أحمد محمد الطالب',
        courseName: 'تطوير المواقع باستخدام HTML و CSS',
        instructorName: 'أحمد محمد',
        completionDate: '15 يناير 2024',
        grade: '95',
        certificateNumber: 'CERT-2024-001',
        duration: '20 ساعة'
    },
    2: {
        studentName: 'أحمد محمد الطالب',
        courseName: 'تصميم واجهات المستخدم UI/UX',
        instructorName: 'سارة أحمد',
        completionDate: '20 فبراير 2024',
        grade: '88',
        certificateNumber: 'CERT-2024-002',
        duration: '28 ساعة'
    }
};

function downloadCertificate(id) {
    const button = event.target;
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
    button.disabled = true;

    // Simulate download process
    setTimeout(() => {
        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;

        // Show success message
        showToast('تم تحميل الشهادة بنجاح!', 'success');
    }, 2000);
}

function viewCertificate(id) {
    const certificateData = certificatesData[id];
    if (!certificateData) {
        showToast('الشهادة غير موجودة', 'error');
        return;
    }

    // Generate certificate HTML
    const certificateHTML = generateCertificateHTML(certificateData);
    document.getElementById('certificatePreview').innerHTML = certificateHTML;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('certificateModal'));
    modal.show();
}

function generateCertificateHTML(data) {
    return `
        <!-- Background Pattern -->
        <div class="certificate-bg-pattern"></div>

        <!-- Decorative Elements -->
        <div class="certificate-decorative-elements">
            <div class="decorative-corner top-left"></div>
            <div class="decorative-corner top-right"></div>
            <div class="decorative-corner bottom-left"></div>
            <div class="decorative-corner bottom-right"></div>
        </div>

        <!-- Main Border -->
        <div class="certificate-border">
            <div class="certificate-inner-border">
                <div class="certificate-content">

                    <!-- Header Section -->
                    <div class="certificate-header-section">
                        <div class="certificate-logo">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h1 class="certificate-title">شهادة إتمام</h1>
                        <p class="certificate-subtitle">معتمدة من منصة التعليم التفاعلي</p>
                    </div>

                    <!-- Body Section -->
                    <div class="certificate-body-section">
                        <p class="certificate-recipient">هذا يشهد أن</p>
                        <h2 class="certificate-name">${data.studentName}</h2>
                        <p class="certificate-description">قد أكمل بنجاح جميع متطلبات دورة</p>
                        <h3 class="certificate-course">${data.courseName}</h3>
                        <p class="certificate-description">
                            وقد أظهر إتقاناً ممتازاً للمهارات والمعارف المطلوبة
                            <br>
                            بإجمالي ${data.duration} من التدريب المكثف
                        </p>
                    </div>

                    <!-- Footer Section -->
                    <div class="certificate-footer-section">
                        <div class="certificate-signature">
                            <div class="signature-line"></div>
                            <div class="signature-name">${data.instructorName}</div>
                            <div class="signature-title">المدرب المعتمد</div>
                        </div>

                        <div class="certificate-grade-section">
                            <div class="certificate-grade">${data.grade}%</div>
                            <div class="certificate-grade-label">الدرجة النهائية</div>
                        </div>

                        <div class="certificate-date-section">
                            <div class="certificate-date">${data.completionDate}</div>
                            <div class="certificate-date-label">تاريخ الإكمال</div>
                        </div>
                    </div>

                    <!-- Certificate Number -->
                    <div class="certificate-number">
                        رقم الشهادة: ${data.certificateNumber}
                    </div>

                </div>
            </div>
        </div>
    `;
}

function printCertificateDirectly(id) {
    const certificateData = certificatesData[id];
    if (!certificateData) {
        showToast('الشهادة غير موجودة', 'error');
        return;
    }

    // Show orientation selection
    showOrientationDialog(certificateData);
}

function showOrientationDialog(certificateData) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">اختر اتجاه الطباعة</h5>
                </div>
                <div class="modal-body text-center">
                    <p class="mb-4">اختر الاتجاه المناسب لطباعة الشهادة:</p>
                    <div class="row g-3">
                        <div class="col-6">
                            <button class="btn btn-outline-primary w-100 h-100 py-4" onclick="printWithOrientation('${certificateData.studentName}', 'portrait')">
                                <i class="fas fa-file-alt fa-3x mb-3 d-block"></i>
                                <div class="fw-bold">طولي</div>
                                <small class="text-muted">Portrait</small>
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-outline-success w-100 h-100 py-4" onclick="printWithOrientation('${certificateData.studentName}', 'landscape')">
                                <i class="fas fa-file fa-3x mb-3 d-block" style="transform: rotate(90deg);"></i>
                                <div class="fw-bold">أفقي</div>
                                <small class="text-muted">Landscape</small>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeOrientationDialog()">إلغاء</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Store modal reference
    window.currentOrientationModal = { modal, bsModal, data: certificateData };
}

function printWithOrientation(studentName, orientation) {
    const { data } = window.currentOrientationModal;
    closeOrientationDialog();

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    const certificateHTML = generatePrintableCertificateHTML(data, orientation);

    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>شهادة - ${data.courseName}</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body {
                    font-family: 'Cairo', sans-serif;
                    margin: 0;
                    padding: 0;
                    width: 100vw;
                    height: 100vh;
                    overflow: hidden;
                }
                ${getPrintStyles(orientation)}
            </style>
        </head>
        <body>
            <div class="certificate-print-area">
                ${certificateHTML}
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 1000);
    };

    showToast(`جاري تحضير الشهادة للطباعة بالوضع ${orientation === 'portrait' ? 'الطولي' : 'الأفقي'}...`, 'info');
}

function closeOrientationDialog() {
    if (window.currentOrientationModal) {
        window.currentOrientationModal.bsModal.hide();
        setTimeout(() => {
            document.body.removeChild(window.currentOrientationModal.modal);
            window.currentOrientationModal = null;
        }, 300);
    }
}

function printCertificate() {
    // Print from modal
    const printContent = document.getElementById('certificatePreview').innerHTML;
    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>طباعة الشهادة</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { font-family: 'Cairo', sans-serif; }
                ${getPrintStyles()}
            </style>
        </head>
        <body>
            <div class="certificate-print-area">
                <div class="certificate-preview">
                    ${printContent}
                </div>
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    };
}

function getPrintStyles(orientation = 'portrait') {
    const isLandscape = orientation === 'landscape';
    const width = isLandscape ? '297mm' : '210mm';
    const height = isLandscape ? '210mm' : '297mm';
    const pageSize = isLandscape ? 'A4 landscape' : 'A4 portrait';

    return `
        .certificate-preview {
            width: ${width};
            height: ${height};
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .certificate-bg-pattern {
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background-image:
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            background-size: 300px 300px, 400px 400px, 200px 200px;
        }

        .certificate-border {
            position: absolute;
            top: ${isLandscape ? '15mm' : '20mm'};
            left: ${isLandscape ? '25mm' : '20mm'};
            right: ${isLandscape ? '25mm' : '20mm'};
            bottom: ${isLandscape ? '15mm' : '20mm'};
            border: 6px solid rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.95);
        }

        .certificate-inner-border {
            position: absolute;
            top: 10px; left: 10px; right: 10px; bottom: 10px;
            border: 3px solid #667eea;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .certificate-content {
            position: relative; height: 100%;
            padding: ${isLandscape ? '25px 40px' : '30px'};
            display: flex; flex-direction: column; justify-content: space-between;
            text-align: center; color: #2d3748;
        }

        .certificate-header-section { margin-bottom: 30px; }

        .certificate-logo {
            width: 120px; height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%; margin: 0 auto 20px;
            display: flex; align-items: center; justify-content: center;
            font-size: 3rem; color: white;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            position: relative;
        }

        .certificate-logo::before {
            content: ''; position: absolute;
            top: -10px; left: -10px; right: -10px; bottom: -10px;
            border: 3px solid #ffd700; border-radius: 50%;
        }

        .certificate-title {
            font-size: 2.5rem; font-weight: 700; color: #667eea;
            margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .certificate-subtitle {
            font-size: 1.2rem; color: #718096; margin-bottom: 40px;
        }

        .certificate-body-section {
            flex-grow: 1; display: flex; flex-direction: column; justify-content: center;
        }

        .certificate-recipient {
            font-size: 1.5rem; color: #4a5568; margin-bottom: 20px;
        }

        .certificate-name {
            font-size: ${isLandscape ? '2.8rem' : '3.2rem'};
            font-weight: 700; color: #2d3748;
            margin: ${isLandscape ? '15px 0' : '20px 0'};
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            position: relative; line-height: 1.2;
        }

        .certificate-name::after {
            content: ''; position: absolute; bottom: -10px; left: 50%;
            transform: translateX(-50%); width: 200px; height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        .certificate-course {
            font-size: ${isLandscape ? '1.6rem' : '1.8rem'};
            font-weight: 600; color: #667eea;
            margin: ${isLandscape ? '20px 0' : '25px 0'};
            line-height: 1.3;
        }

        .certificate-description {
            font-size: ${isLandscape ? '1rem' : '1.1rem'};
            color: #4a5568;
            margin-bottom: ${isLandscape ? '25px' : '30px'};
            line-height: 1.5;
        }

        .certificate-footer-section {
            display: flex; justify-content: space-between;
            align-items: end; margin-top: 40px;
        }

        .certificate-signature {
            text-align: center; min-width: 200px;
        }

        .signature-line {
            border-top: 3px solid #2d3748; margin-bottom: 10px;
            position: relative;
        }

        .signature-line::before {
            content: ''; position: absolute; top: -6px; left: 50%;
            transform: translateX(-50%); width: 20px; height: 12px;
            background: #667eea; border-radius: 6px;
        }

        .signature-name {
            font-weight: 700; font-size: 1.1rem;
            color: #2d3748; margin-bottom: 5px;
        }

        .signature-title {
            font-size: 0.9rem; color: #718096;
        }

        .certificate-grade-section {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 20px; border-radius: 15px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            min-width: 150px;
        }

        .certificate-grade {
            font-size: 2.5rem; font-weight: 700; margin-bottom: 5px;
        }

        .certificate-grade-label {
            font-size: 0.9rem; opacity: 0.9;
        }

        .certificate-date-section {
            text-align: center; min-width: 200px;
        }

        .certificate-date {
            font-weight: 700; font-size: 1.1rem;
            color: #2d3748; margin-bottom: 5px;
        }

        .certificate-date-label {
            font-size: 0.9rem; color: #718096;
        }

        .certificate-number {
            position: absolute; bottom: 30px; left: 50%;
            transform: translateX(-50%); font-size: 0.8rem;
            color: #718096; background: rgba(255, 255, 255, 0.8);
            padding: 5px 15px; border-radius: 15px;
            border: 1px solid #e2e8f0;
        }

        .decorative-corner {
            position: absolute; width: 80px; height: 80px;
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            opacity: 0.3;
        }

        .decorative-corner.top-left {
            top: 20px; left: 20px; border-radius: 0 0 50px 0;
        }

        .decorative-corner.top-right {
            top: 20px; right: 20px; border-radius: 0 0 0 50px;
        }

        .decorative-corner.bottom-left {
            bottom: 20px; left: 20px; border-radius: 0 50px 0 0;
        }

        .decorative-corner.bottom-right {
            bottom: 20px; right: 20px; border-radius: 50px 0 0 0;
        }

        @page {
            size: ${pageSize};
            margin: 0;
        }
    `;
}

function generatePrintableCertificateHTML(data, orientation = 'portrait') {
    return `
        <div class="certificate-preview ${orientation === 'landscape' ? 'certificate-landscape' : ''}">
            ${generateCertificateHTML(data)}
        </div>
    `;
}

function downloadCertificatePDF() {
    // In a real application, this would generate an actual PDF
    showToast('تم تحميل الشهادة كملف PDF!', 'success');
}

function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.style.position = 'fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // Add to page
    document.body.appendChild(toast);

    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove after hiding
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Animation on load
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.certificate-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
</script>

</body>
</html>
