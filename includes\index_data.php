<?php
// Data handler for index.php
require_once 'config/database.php';

// Function to get featured courses
function getFeaturedCourses($limit = 6) {
    try {
        $courses = fetchAll("
            SELECT 
                c.*,
                u.full_name as instructor_name,
                u.profile_image as instructor_avatar,
                cat.name as category_name,
                COUNT(DISTINCT e.id) as enrolled_students,
                COUNT(DISTINCT r.id) as total_reviews,
                COALESCE(AVG(r.rating), 0) as average_rating
            FROM courses c
            LEFT JOIN users u ON c.instructor_id = u.id
            LEFT JOIN categories cat ON c.category_id = cat.id
            LEFT JOIN enrollments e ON c.id = e.course_id
            LEFT JOIN reviews r ON c.id = r.course_id
            WHERE c.status = 'published'
            GROUP BY c.id
            ORDER BY c.enrolled_students DESC, c.rating DESC, c.created_at DESC
            LIMIT ?
        ", [$limit]);
        
        if (empty($courses)) {
            // Fallback to sample data if no courses in database
            return getSampleCourses();
        }
        
        return $courses;
    } catch (Exception $e) {
        error_log("Error fetching featured courses: " . $e->getMessage());
        return getSampleCourses();
    }
}

// Function to get site statistics
function getSiteStatistics() {
    try {
        $stats = fetchAll("SELECT stat_name, stat_value, display_value FROM site_statistics");
        
        if (empty($stats)) {
            // Calculate real-time statistics if no data in statistics table
            $realStats = [
                'total_students' => fetch("SELECT COUNT(*) as count FROM users WHERE role = 'student'")['count'] ?? 0,
                'total_courses' => fetch("SELECT COUNT(*) as count FROM courses WHERE status = 'published'")['count'] ?? 0,
                'total_instructors' => fetch("SELECT COUNT(*) as count FROM users WHERE role = 'instructor'")['count'] ?? 0,
                'satisfaction_rate' => 98
            ];
            
            return [
                'total_students' => $realStats['total_students'] > 0 ? number_format($realStats['total_students']) . '+' : '15,000+',
                'total_courses' => $realStats['total_courses'] > 0 ? number_format($realStats['total_courses']) . '+' : '500+',
                'total_instructors' => $realStats['total_instructors'] > 0 ? number_format($realStats['total_instructors']) . '+' : '150+',
                'satisfaction_rate' => $realStats['satisfaction_rate'] . '%'
            ];
        }
        
        $result = [];
        foreach ($stats as $stat) {
            $result[$stat['stat_name']] = $stat['display_value'];
        }
        
        return $result;
    } catch (Exception $e) {
        error_log("Error fetching site statistics: " . $e->getMessage());
        return [
            'total_students' => '15,000+',
            'total_courses' => '500+',
            'total_instructors' => '150+',
            'satisfaction_rate' => '98%'
        ];
    }
}

// Function to get featured testimonials
function getFeaturedTestimonials($limit = 3) {
    try {
        $testimonials = fetchAll("
            SELECT 
                t.*,
                c.title as course_title
            FROM testimonials t
            LEFT JOIN courses c ON t.course_id = c.id
            WHERE t.is_featured = TRUE AND t.status = 'approved'
            ORDER BY t.created_at DESC
            LIMIT ?
        ", [$limit]);
        
        if (empty($testimonials)) {
            return getSampleTestimonials();
        }
        
        return $testimonials;
    } catch (Exception $e) {
        error_log("Error fetching testimonials: " . $e->getMessage());
        return getSampleTestimonials();
    }
}

// Function to get categories with course counts
function getCategoriesWithCounts($limit = 8) {
    try {
        $categories = fetchAll("
            SELECT 
                cat.*,
                COUNT(c.id) as course_count
            FROM categories cat
            LEFT JOIN courses c ON cat.id = c.category_id AND c.status = 'published'
            GROUP BY cat.id
            ORDER BY course_count DESC, cat.name ASC
            LIMIT ?
        ", [$limit]);
        
        if (empty($categories)) {
            return getSampleCategories();
        }
        
        return $categories;
    } catch (Exception $e) {
        error_log("Error fetching categories: " . $e->getMessage());
        return getSampleCategories();
    }
}

// Function to handle newsletter subscription
function subscribeToNewsletter($email) {
    try {
        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return ['success' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
        }
        
        // Check if already subscribed
        $existing = fetch("SELECT id FROM newsletter_subscriptions WHERE email = ?", [$email]);
        if ($existing) {
            return ['success' => false, 'message' => 'هذا البريد مشترك بالفعل'];
        }
        
        // Insert new subscription
        $result = insert('newsletter_subscriptions', ['email' => $email]);
        
        if ($result) {
            return ['success' => true, 'message' => 'تم الاشتراك بنجاح!'];
        } else {
            return ['success' => false, 'message' => 'حدث خطأ، يرجى المحاولة لاحقاً'];
        }
    } catch (Exception $e) {
        error_log("Error subscribing to newsletter: " . $e->getMessage());
        return ['success' => false, 'message' => 'حدث خطأ، يرجى المحاولة لاحقاً'];
    }
}

// Sample data functions (fallback)
function getSampleCourses() {
    return [
        [
            'id' => 1,
            'title' => 'دورة تطوير المواقع الشاملة',
            'description' => 'تعلم تطوير المواقع من الصفر حتى الاحتراف',
            'instructor_name' => 'أحمد محمد',
            'instructor_avatar' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
            'category_name' => 'البرمجة',
            'price' => 499.00,
            'discount_price' => 299.00,
            'thumbnail' => 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&h=600&fit=crop',
            'level' => 'beginner',
            'duration' => 720,
            'enrolled_students' => 1234,
            'total_reviews' => 456,
            'average_rating' => 4.9
        ],
        [
            'id' => 2,
            'title' => 'أساسيات التصميم الجرافيكي',
            'description' => 'تعلم أساسيات التصميم الجرافيكي باستخدام Adobe',
            'instructor_name' => 'فاطمة أحمد',
            'instructor_avatar' => 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
            'category_name' => 'التصميم',
            'price' => 199.00,
            'discount_price' => null,
            'thumbnail' => 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&h=600&fit=crop',
            'level' => 'beginner',
            'duration' => 480,
            'enrolled_students' => 856,
            'total_reviews' => 234,
            'average_rating' => 4.7
        ],
        [
            'id' => 3,
            'title' => 'استراتيجيات التسويق الرقمي',
            'description' => 'تعلم أحدث استراتيجيات التسويق الرقمي',
            'instructor_name' => 'محمد علي',
            'instructor_avatar' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
            'category_name' => 'التسويق',
            'price' => 399.00,
            'discount_price' => 249.00,
            'thumbnail' => 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
            'level' => 'intermediate',
            'duration' => 900,
            'enrolled_students' => 2156,
            'total_reviews' => 678,
            'average_rating' => 4.8
        ]
    ];
}

function getSampleTestimonials() {
    return [
        [
            'name' => 'أحمد محمد',
            'position' => 'مطور ويب',
            'avatar' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
            'content' => 'منصة رائعة ساعدتني في تطوير مهاراتي في البرمجة. المحتوى عالي الجودة والمدربين محترفين جداً.',
            'rating' => 5,
            'course_title' => 'دورة تطوير المواقع الشاملة'
        ],
        [
            'name' => 'فاطمة أحمد',
            'position' => 'مصممة جرافيك',
            'avatar' => 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
            'content' => 'تعلمت التصميم الجرافيكي من الصفر وأصبحت الآن أعمل كمصممة مستقلة. شكراً لهذه المنصة الرائعة.',
            'rating' => 5,
            'course_title' => 'أساسيات التصميم الجرافيكي'
        ],
        [
            'name' => 'محمد علي',
            'position' => 'مختص تسويق رقمي',
            'avatar' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
            'content' => 'دورات التسويق الرقمي غيرت مسار حياتي المهنية. المحتوى عملي ومفيد جداً للتطبيق الفوري.',
            'rating' => 5,
            'course_title' => 'استراتيجيات التسويق الرقمي'
        ]
    ];
}

function getSampleCategories() {
    return [
        ['id' => 1, 'name' => 'البرمجة وتطوير المواقع', 'course_count' => 120],
        ['id' => 2, 'name' => 'التصميم والجرافيك', 'course_count' => 85],
        ['id' => 3, 'name' => 'إدارة الأعمال', 'course_count' => 95],
        ['id' => 4, 'name' => 'التسويق الرقمي', 'course_count' => 70],
        ['id' => 5, 'name' => 'تحليل البيانات', 'course_count' => 60],
        ['id' => 6, 'name' => 'اللغات', 'course_count' => 45],
        ['id' => 7, 'name' => 'التصوير والمونتاج', 'course_count' => 35],
        ['id' => 8, 'name' => 'الموسيقى والفنون', 'course_count' => 25]
    ];
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'subscribe_newsletter':
            $email = trim($_POST['email'] ?? '');
            $result = subscribeToNewsletter($email);
            echo json_encode($result);
            exit;
            
        default:
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
            exit;
    }
}
?>
