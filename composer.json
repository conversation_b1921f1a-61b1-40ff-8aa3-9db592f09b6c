{"name": "elearning/platform", "description": "Interactive Learning Platform", "type": "project", "require": {"php": "^7.4|^8.0", "phpmailer/phpmailer": "^6.8", "stripe/stripe-php": "^10.0", "paypal/rest-api-sdk-php": "^1.14", "vlucas/phpdotenv": "^5.5", "intervention/image": "^2.7", "guzzlehttp/guzzle": "^7.0", "firebase/php-jwt": "^6.0", "monolog/monolog": "^2.0", "nesbot/carbon": "^2.0", "phpoffice/phpspreadsheet": "^1.29", "dompdf/dompdf": "^2.0", "predis/predis": "^2.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "symfony/var-dumper": "^5.0", "filp/whoops": "^2.0", "fakerphp/faker": "^1.0", "phpstan/phpstan": "^1.0", "squizlabs/php_codesniffer": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/"}, "files": ["config/config.php", "config/database_auto.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "test": "phpunit", "check-style": "phpcs", "fix-style": "phpcbf"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}