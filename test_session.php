<?php
// Test script to check session functionality
require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>اختبار الجلسات والتكوين</h2>";

// Test session status
echo "<h3>حالة الجلسة:</h3>";
echo "<p>Session Status: " . session_status() . "</p>";
echo "<p>Session ID: " . session_id() . "</p>";

// Test session variables
echo "<h3>متغيرات الجلسة:</h3>";
if (empty($_SESSION)) {
    echo "<p style='color: orange;'>لا توجد متغيرات جلسة</p>";
} else {
    echo "<pre>";
    print_r($_SESSION);
    echo "</pre>";
}

// Test authentication functions
echo "<h3>اختبار دوال المصادقة:</h3>";
echo "<p>isAuthenticated(): " . (isAuthenticated() ? 'true' : 'false') . "</p>";
echo "<p>isAdmin(): " . (isAdmin() ? 'true' : 'false') . "</p>";
echo "<p>isInstructor(): " . (isInstructor() ? 'true' : 'false') . "</p>";

// Test database connection
echo "<h3>اختبار قاعدة البيانات:</h3>";
try {
    $test_query = $conn->query("SELECT 1");
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات يعمل</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// Test users table
try {
    $user_count = fetch("SELECT COUNT(*) as count FROM users");
    echo "<p style='color: green;'>✓ جدول المستخدمين موجود ويحتوي على " . $user_count['count'] . " مستخدم</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في جدول المستخدمين: " . $e->getMessage() . "</p>";
}

// Test configuration constants
echo "<h3>اختبار الثوابت:</h3>";
echo "<p>APP_NAME: " . (defined('APP_NAME') ? APP_NAME : 'غير محدد') . "</p>";
echo "<p>APP_URL: " . (defined('APP_URL') ? APP_URL : 'غير محدد') . "</p>";
echo "<p>DB_NAME: " . (defined('DB_NAME') ? DB_NAME : 'غير محدد') . "</p>";

// Test CSRF token
echo "<h3>اختبار CSRF Token:</h3>";
$csrf_token = csrf_token();
echo "<p>CSRF Token: " . substr($csrf_token, 0, 20) . "...</p>";

echo "<hr>";
echo "<p><a href='login.php'>الذهاب إلى صفحة تسجيل الدخول</a></p>";
echo "<p><a href='register.php'>الذهاب إلى صفحة التسجيل</a></p>";
?>
