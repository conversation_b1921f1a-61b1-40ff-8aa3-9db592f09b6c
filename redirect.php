<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>توجيه - منصة التعليم التفاعلي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .redirect-container {
            max-width: 600px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            text-align: center;
        }
        .redirect-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
        }
        .redirect-content {
            padding: 2rem;
        }
        .btn-redirect {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        .btn-redirect:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        .loading {
            display: none;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="redirect-header">
            <h1><i class="fas fa-route me-3"></i>توجيه الصفحات</h1>
            <p class="mb-0">اختر الصفحة التي تريد زيارتها</p>
        </div>
        
        <div class="redirect-content">
            <div id="normalView">
                <h4 class="mb-4">الصفحات المتاحة:</h4>
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <a href="index.php" class="btn-redirect w-100">
                            <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="courses.php" class="btn-redirect w-100">
                            <i class="fas fa-graduation-cap me-2"></i>الدورات
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="instructors.php" class="btn-redirect w-100">
                            <i class="fas fa-chalkboard-teacher me-2"></i>المدربين
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="login.php" class="btn-redirect w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                </div>
                
                <hr class="my-4">
                
                <h5>لوحات التحكم:</h5>
                <div class="row g-3">
                    <div class="col-md-4">
                        <button onclick="goToInstructor()" class="btn-redirect w-100">
                            <i class="fas fa-user-tie me-2"></i>لوحة المدرب
                        </button>
                    </div>
                    <div class="col-md-4">
                        <a href="student/dashboard.php" class="btn-redirect w-100">
                            <i class="fas fa-user-graduate me-2"></i>لوحة الطالب
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="admin/dashboard.php" class="btn-redirect w-100">
                            <i class="fas fa-cog me-2"></i>لوحة الإدارة
                        </a>
                    </div>
                </div>
                
                <hr class="my-4">
                
                <h5>أدوات التشخيص:</h5>
                <div class="row g-3">
                    <div class="col-md-4">
                        <a href="test_connection.php" class="btn-redirect w-100">
                            <i class="fas fa-database me-2"></i>اختبار الاتصال
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="database_setup.html" class="btn-redirect w-100">
                            <i class="fas fa-tools me-2"></i>إعداد قاعدة البيانات
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="complete_database_fix.php" class="btn-redirect w-100">
                            <i class="fas fa-wrench me-2"></i>إصلاح شامل
                        </a>
                    </div>
                </div>
            </div>
            
            <div id="loadingView" class="loading">
                <div class="spinner"></div>
                <h5>جاري التوجيه...</h5>
                <p>يرجى الانتظار قليلاً</p>
            </div>
        </div>
    </div>

    <script>
        function showLoading() {
            document.getElementById('normalView').style.display = 'none';
            document.getElementById('loadingView').style.display = 'block';
        }
        
        function goToInstructor() {
            showLoading();
            
            // Try multiple possible paths
            const paths = [
                'instructor/dashboard.php',
                './instructor/dashboard.php',
                '/التعليم التفاعلي الالكتروني/instructor/dashboard.php'
            ];
            
            // Try the first path
            setTimeout(() => {
                window.location.href = paths[0];
            }, 1000);
        }
        
        // Add click handlers to all links
        document.querySelectorAll('.btn-redirect').forEach(link => {
            if (!link.onclick) {
                link.addEventListener('click', function(e) {
                    if (this.href) {
                        showLoading();
                    }
                });
            }
        });
        
        // Auto-redirect if coming from a specific error
        const urlParams = new URLSearchParams(window.location.search);
        const redirect = urlParams.get('redirect');
        
        if (redirect === 'instructor') {
            setTimeout(() => {
                goToInstructor();
            }, 2000);
        }
    </script>
</body>
</html>
