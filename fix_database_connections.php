<?php
// <PERSON><PERSON><PERSON> to fix all database connections in the project
echo "🔧 إصلاح اتصالات قاعدة البيانات في جميع ملفات المشروع...\n\n";

// Define the files that need to be updated
$files_to_update = [
    // Root level files
    'courses.php',
    'course.php', 
    'instructors.php',
    'instructor.php',
    'login.php',
    'register.php',
    'forgot-password.php',
    'reset-password.php',
    'contact.php',
    'about.php',
    'search.php',
    'logout.php',
    
    // Test files
    'test_courses.php',
    'test_instructors.php', 
    'test_paths.php',
    'test_registration.php',
    'test_connection.php',
    
    // Student directory
    'student/dashboard.php',
    'student/profile.php',
    'student/courses.php',
    'student/certificates.php',
    'student/settings.php',
    'student/wishlist.php',
    'student/progress.php',
    
    // Instructor directory
    'instructor/dashboard.php',
    'instructor/courses.php',
    'instructor/add-course.php',
    'instructor/edit-course.php',
    'instructor/students.php',
    'instructor/profile.php',
    'instructor/analytics.php',
    'instructor/settings.php',
    
    // Admin directory
    'admin/dashboard.php',
    'admin/users.php',
    'admin/courses.php',
    'admin/categories.php',
    'admin/settings.php',
    'admin/reports.php',
    'admin/payments.php',
    
    // API directory
    'api/courses.php',
    'api/users.php',
    'api/auth.php',
    'api/search.php',
    'api/upload.php',
];

$updated_count = 0;
$error_count = 0;

foreach ($files_to_update as $file) {
    if (file_exists($file)) {
        try {
            $content = file_get_contents($file);
            $original_content = $content;
            
            // Replace database.php with database_auto.php
            $content = str_replace(
                "require_once 'config/database.php';",
                "require_once 'config/database_auto.php';",
                $content
            );
            
            $content = str_replace(
                "require_once '../config/database.php';",
                "require_once '../config/database_auto.php';",
                $content
            );
            
            $content = str_replace(
                "require_once '../../config/database.php';",
                "require_once '../../config/database_auto.php';",
                $content
            );
            
            // Check if content was changed
            if ($content !== $original_content) {
                file_put_contents($file, $content);
                echo "✅ تم تحديث: $file\n";
                $updated_count++;
            } else {
                echo "ℹ️  لا يحتاج تحديث: $file\n";
            }
            
        } catch (Exception $e) {
            echo "❌ خطأ في تحديث $file: " . $e->getMessage() . "\n";
            $error_count++;
        }
    } else {
        echo "⚠️  الملف غير موجود: $file\n";
    }
}

// Update composer.json
if (file_exists('composer.json')) {
    try {
        $content = file_get_contents('composer.json');
        $content = str_replace(
            '"config/database.php"',
            '"config/database_auto.php"',
            $content
        );
        file_put_contents('composer.json', $content);
        echo "✅ تم تحديث: composer.json\n";
        $updated_count++;
    } catch (Exception $e) {
        echo "❌ خطأ في تحديث composer.json: " . $e->getMessage() . "\n";
        $error_count++;
    }
}

echo "\n📊 ملخص النتائج:\n";
echo "✅ تم تحديث $updated_count ملف\n";
echo "❌ أخطاء في $error_count ملف\n";

// Test the database connection
echo "\n🔍 اختبار الاتصال بقاعدة البيانات...\n";
try {
    require_once 'config/database_auto.php';
    echo "✅ نجح الاتصال بقاعدة البيانات!\n";
    echo "🏠 المضيف: " . DB_HOST . "\n";
    echo "👤 المستخدم: " . DB_USER . "\n";
    echo "🗄️  قاعدة البيانات: " . DB_NAME . "\n";
} catch (Exception $e) {
    echo "❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
}

echo "\n🎉 انتهى إصلاح اتصالات قاعدة البيانات!\n";
?>
