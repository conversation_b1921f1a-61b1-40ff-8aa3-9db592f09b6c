<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة دورة جديدة - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
            --shadow-medium: 0 10px 30px rgba(0,0,0,0.1);
            --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
            --border-radius: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: var(--shadow-medium);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: white !important;
        }

        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            margin: 0 5px;
            padding: 8px 16px !important;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white !important;
            transform: translateY(-1px);
        }

        .hero-section {
            background: var(--primary-gradient);
            color: white;
            padding: 3rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .form-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-heavy);
            padding: 2.5rem;
            margin-top: -2rem;
            position: relative;
            z-index: 2;
            border: none;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            transition: var(--transition);
            font-size: 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-gradient:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .form-step {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-light);
            border-left: 4px solid #667eea;
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-gradient);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 1rem;
        }

        .file-upload-area {
            border: 2px dashed #667eea;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.05);
        }

        .breadcrumb {
            background: transparent;
            padding: 1rem 0;
        }

        .breadcrumb-item a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: white;
        }

        .price-input-group {
            position: relative;
        }

        .price-input-group .form-control {
            padding-right: 50px;
        }

        .price-currency {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #718096;
            font-weight: 600;
        }

        /* Advanced Features */
        .progress-bar-container {
            background: #e2e8f0;
            border-radius: 10px;
            height: 8px;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .progress-bar {
            background: var(--primary-gradient);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            position: relative;
        }

        .step-indicator::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e2e8f0;
            z-index: 1;
        }

        .step-indicator-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
            background: white;
            padding: 0 10px;
        }

        .step-indicator-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            color: #718096;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 0.5rem;
            transition: var(--transition);
        }

        .step-indicator-item.active .step-indicator-number {
            background: var(--primary-gradient);
            color: white;
        }

        .step-indicator-item.completed .step-indicator-number {
            background: var(--success-gradient);
            color: white;
        }

        .step-indicator-label {
            font-size: 0.8rem;
            color: #718096;
            text-align: center;
        }

        .step-indicator-item.active .step-indicator-label {
            color: #667eea;
            font-weight: 600;
        }

        .form-floating-label {
            position: relative;
        }

        .form-floating-label .form-control:focus + .floating-label,
        .form-floating-label .form-control:not(:placeholder-shown) + .floating-label {
            top: -8px;
            font-size: 0.8rem;
            color: #667eea;
            background: white;
            padding: 0 5px;
        }

        .floating-label {
            position: absolute;
            top: 50%;
            right: 16px;
            transform: translateY(-50%);
            background: transparent;
            color: #718096;
            transition: var(--transition);
            pointer-events: none;
            z-index: 3;
        }

        .char-counter {
            font-size: 0.8rem;
            color: #718096;
            text-align: left;
            margin-top: 0.25rem;
        }

        .char-counter.warning {
            color: #f56565;
        }

        .feature-highlight {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .feature-highlight h6 {
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .feature-highlight p {
            color: #718096;
            margin-bottom: 0;
            font-size: 0.9rem;
        }

        .input-group-addon {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-right: none;
            border-radius: 12px 0 0 12px;
            padding: 12px 16px;
            color: #718096;
            font-weight: 600;
        }

        .input-group .form-control {
            border-right: none;
            border-radius: 0 12px 12px 0;
        }

        .tooltip-icon {
            color: #667eea;
            cursor: help;
            margin-right: 5px;
        }

        .preview-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow-light);
            margin-top: 1rem;
            border: 2px dashed #e2e8f0;
        }

        .preview-card.has-content {
            border: 2px solid #667eea;
        }

        .preview-thumbnail {
            width: 100%;
            height: 200px;
            background: #f7fafc;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #cbd5e0;
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .preview-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .preview-description {
            color: #718096;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .preview-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #718096;
        }

        .save-draft-btn {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            color: #718096;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: var(--transition);
        }

        .save-draft-btn:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
            color: #4a5568;
        }

        .form-tips {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-tips h6 {
            color: #22543d;
            margin-bottom: 0.5rem;
        }

        .form-tips ul {
            margin-bottom: 0;
            padding-right: 1rem;
        }

        .form-tips li {
            color: #2f855a;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .advanced-options {
            background: #fafafa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 1rem;
            border: 1px solid #e2e8f0;
        }

        .advanced-toggle {
            cursor: pointer;
            color: #667eea;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .advanced-toggle i {
            transition: var(--transition);
        }

        .advanced-toggle.collapsed i {
            transform: rotate(180deg);
        }

        .floating-action-btn {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-gradient);
            color: white;
            border: none;
            box-shadow: var(--shadow-heavy);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            transition: var(--transition);
            z-index: 1000;
        }

        .floating-action-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 25px 50px rgba(102, 126, 234, 0.4);
        }

        @media (max-width: 768px) {
            .step-indicator {
                flex-wrap: wrap;
                gap: 1rem;
            }

            .step-indicator-item {
                flex: 1;
                min-width: calc(50% - 0.5rem);
            }

            .floating-action-btn {
                bottom: 20px;
                left: 20px;
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="../index.php">
            <i class="fas fa-graduation-cap me-2"></i>
            منصة التعليم التفاعلي
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">لوحة التحكم</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="courses.php">دوراتي</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="add-course.php">إضافة دورة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="students.php">الطلاب</a>
                </li>
            </ul>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn btn-link text-white text-decoration-none dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2"></i>
                        مدرب تجريبي
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i> الملف الشخصي
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="dashboard.php">لوحة التحكم</a></li>
                <li class="breadcrumb-item active">إضافة دورة جديدة</li>
            </ol>
        </nav>
        
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-3">إضافة دورة جديدة</h1>
                <p class="lead mb-0">أنشئ دورة تعليمية جديدة وشاركها مع الطلاب</p>
            </div>
            <div class="col-md-4 text-center">
                <i class="fas fa-plus-circle" style="font-size: 5rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Add Course Form -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-card">
                    <!-- Demo Alert -->
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>هذه نسخة تجريبية من صفحة إضافة الدورة. التصميم يعمل بشكل كامل!
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>

                    <!-- Progress Indicator -->
                    <div class="step-indicator">
                        <div class="step-indicator-item active" data-step="1">
                            <div class="step-indicator-number">1</div>
                            <div class="step-indicator-label">المعلومات الأساسية</div>
                        </div>
                        <div class="step-indicator-item" data-step="2">
                            <div class="step-indicator-number">2</div>
                            <div class="step-indicator-label">تفاصيل الدورة</div>
                        </div>
                        <div class="step-indicator-item" data-step="3">
                            <div class="step-indicator-number">3</div>
                            <div class="step-indicator-label">التسعير</div>
                        </div>
                        <div class="step-indicator-item" data-step="4">
                            <div class="step-indicator-number">4</div>
                            <div class="step-indicator-label">صورة الدورة</div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progressBar"></div>
                    </div>

                    <!-- Form Tips -->
                    <div class="form-tips">
                        <h6><i class="fas fa-lightbulb me-2"></i>نصائح لإنشاء دورة ناجحة</h6>
                        <ul>
                            <li>اختر عنواناً واضحاً وجذاباً يصف محتوى الدورة</li>
                            <li>اكتب وصفاً شاملاً يوضح ما سيتعلمه الطلاب</li>
                            <li>حدد السعر المناسب بناءً على قيمة المحتوى</li>
                            <li>استخدم صورة عالية الجودة تمثل موضوع الدورة</li>
                        </ul>
                    </div>

                    <form method="POST" action="" enctype="multipart/form-data" class="needs-validation" novalidate>
                        <!-- Step 1: Basic Information -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">1</div>
                                <h4 class="mb-0">المعلومات الأساسية</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="title" class="form-label">
                                        عنوان الدورة *
                                        <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip"
                                           title="اختر عنواناً واضحاً وجذاباً يصف محتوى الدورة"></i>
                                    </label>
                                    <input type="text" class="form-control" id="title" name="title"
                                           placeholder="مثال: دورة تطوير المواقع الشاملة" required maxlength="100">
                                    <div class="char-counter" id="titleCounter">0/100 حرف</div>
                                    <div class="invalid-feedback">يرجى إدخال عنوان الدورة</div>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="category_id" class="form-label">الفئة *</label>
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="1">💻 البرمجة وتطوير المواقع</option>
                                        <option value="2">🎨 التصميم والجرافيك</option>
                                        <option value="3">📈 التسويق الرقمي</option>
                                        <option value="4">💼 إدارة الأعمال</option>
                                        <option value="5">📊 تحليل البيانات</option>
                                        <option value="6">🔒 الأمن السيبراني</option>
                                        <option value="7">📱 تطوير التطبيقات</option>
                                        <option value="8">🎯 ريادة الأعمال</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار فئة الدورة</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    وصف الدورة *
                                    <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip"
                                       title="اكتب وصفاً شاملاً يوضح أهداف الدورة وما سيتعلمه الطلاب"></i>
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="6"
                                          placeholder="اكتب وصفاً شاملاً للدورة يتضمن:&#10;• أهداف الدورة&#10;• المهارات التي سيكتسبها الطلاب&#10;• المتطلبات المسبقة (إن وجدت)&#10;• محتوى الدورة بشكل عام"
                                          required maxlength="1000"></textarea>
                                <div class="char-counter" id="descriptionCounter">0/1000 حرف</div>
                                <div class="invalid-feedback">يرجى إدخال وصف الدورة</div>
                            </div>

                            <!-- Learning Objectives -->
                            <div class="mb-3">
                                <label for="objectives" class="form-label">
                                    أهداف التعلم
                                    <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip"
                                       title="حدد 3-5 أهداف رئيسية سيحققها الطلاب"></i>
                                </label>
                                <div id="objectivesList">
                                    <div class="input-group mb-2">
                                        <span class="input-group-addon">1.</span>
                                        <input type="text" class="form-control" name="objectives[]"
                                               placeholder="مثال: إتقان أساسيات HTML و CSS">
                                    </div>
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="addObjective">
                                    <i class="fas fa-plus me-1"></i>إضافة هدف آخر
                                </button>
                            </div>
                        </div>

                        <!-- Step 2: Course Details -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">2</div>
                                <h4 class="mb-0">تفاصيل الدورة</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="level" class="form-label">مستوى الدورة</label>
                                    <select class="form-select" id="level" name="level">
                                        <option value="beginner">🟢 مبتدئ - لا يتطلب خبرة سابقة</option>
                                        <option value="intermediate">🟡 متوسط - يتطلب معرفة أساسية</option>
                                        <option value="advanced">🔴 متقدم - يتطلب خبرة واسعة</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="language" class="form-label">لغة الدورة</label>
                                    <select class="form-select" id="language" name="language">
                                        <option value="Arabic">🇸🇦 العربية</option>
                                        <option value="English">🇺🇸 الإنجليزية</option>
                                        <option value="French">🇫🇷 الفرنسية</option>
                                        <option value="Spanish">🇪🇸 الإسبانية</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="duration" class="form-label">
                                        مدة الدورة الإجمالية
                                        <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip"
                                           title="المدة التقديرية لإكمال جميع دروس الدورة"></i>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="duration" name="duration"
                                               placeholder="8" min="1" max="500">
                                        <span class="input-group-text">ساعة</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Advanced Options -->
                            <div class="advanced-options">
                                <div class="advanced-toggle" data-bs-toggle="collapse" data-bs-target="#advancedOptions">
                                    <span><i class="fas fa-cog me-2"></i>خيارات متقدمة</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>

                                <div class="collapse" id="advancedOptions">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="prerequisites" class="form-label">المتطلبات المسبقة</label>
                                            <textarea class="form-control" id="prerequisites" name="prerequisites" rows="3"
                                                      placeholder="مثال: معرفة أساسية بالحاسوب، خبرة في استخدام الإنترنت"></textarea>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="target_audience" class="form-label">الجمهور المستهدف</label>
                                            <textarea class="form-control" id="target_audience" name="target_audience" rows="3"
                                                      placeholder="مثال: المطورين المبتدئين، طلاب الجامعات، المهتمين بالتقنية"></textarea>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="certificate" class="form-label">شهادة إتمام</label>
                                            <select class="form-select" id="certificate" name="certificate">
                                                <option value="1">✅ متاحة</option>
                                                <option value="0">❌ غير متاحة</option>
                                            </select>
                                        </div>

                                        <div class="col-md-4 mb-3">
                                            <label for="difficulty_rating" class="form-label">تقييم الصعوبة</label>
                                            <select class="form-select" id="difficulty_rating" name="difficulty_rating">
                                                <option value="1">⭐ سهل جداً</option>
                                                <option value="2">⭐⭐ سهل</option>
                                                <option value="3">⭐⭐⭐ متوسط</option>
                                                <option value="4">⭐⭐⭐⭐ صعب</option>
                                                <option value="5">⭐⭐⭐⭐⭐ صعب جداً</option>
                                            </select>
                                        </div>

                                        <div class="col-md-4 mb-3">
                                            <label for="estimated_lessons" class="form-label">عدد الدروس المتوقع</label>
                                            <input type="number" class="form-control" id="estimated_lessons" name="estimated_lessons"
                                                   placeholder="15" min="1" max="100">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Pricing -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">3</div>
                                <h4 class="mb-0">التسعير</h4>
                            </div>

                            <div class="feature-highlight">
                                <h6><i class="fas fa-dollar-sign me-2"></i>استراتيجية التسعير</h6>
                                <p>حدد سعراً عادلاً يعكس قيمة المحتوى. يمكنك البدء بسعر منخفض وزيادته لاحقاً بناءً على التقييمات.</p>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="pricing_type" class="form-label">نوع التسعير</label>
                                    <select class="form-select" id="pricing_type" name="pricing_type">
                                        <option value="free">🆓 مجاني</option>
                                        <option value="paid" selected>💰 مدفوع</option>
                                        <option value="subscription">📅 اشتراك شهري</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3" id="priceGroup">
                                    <label for="price" class="form-label">
                                        سعر الدورة *
                                        <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip"
                                           title="السعر المقترح للدورات المشابهة: 50-500 ريال"></i>
                                    </label>
                                    <div class="price-input-group">
                                        <input type="number" class="form-control" id="price" name="price"
                                               placeholder="199" min="0" step="1" required>
                                        <span class="price-currency">ريال</span>
                                    </div>
                                    <div class="invalid-feedback">يرجى إدخال سعر الدورة</div>
                                </div>

                                <div class="col-md-4 mb-3" id="discountGroup">
                                    <label for="discount_price" class="form-label">سعر الخصم (اختياري)</label>
                                    <div class="price-input-group">
                                        <input type="number" class="form-control" id="discount_price" name="discount_price"
                                               placeholder="149" min="0" step="1">
                                        <span class="price-currency">ريال</span>
                                    </div>
                                    <small class="text-success" id="discountPercentage"></small>
                                </div>
                            </div>

                            <!-- Pricing Suggestions -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex gap-2 mb-3">
                                        <span class="text-muted">أسعار مقترحة:</span>
                                        <button type="button" class="btn btn-outline-secondary btn-sm price-suggestion" data-price="99">99 ريال</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm price-suggestion" data-price="199">199 ريال</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm price-suggestion" data-price="299">299 ريال</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm price-suggestion" data-price="499">499 ريال</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4: Course Thumbnail -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">4</div>
                                <h4 class="mb-0">صورة الدورة</h4>
                            </div>

                            <div class="mb-3">
                                <label for="thumbnail" class="form-label">صورة مصغرة للدورة</label>
                                <div class="file-upload-area" id="fileUploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h5>اسحب وأفلت الصورة هنا أو انقر للاختيار</h5>
                                    <p class="text-muted mb-0">الحد الأقصى: 5MB | الأنواع المدعومة: JPG, PNG, GIF, WebP</p>
                                    <input type="file" class="form-control d-none" id="thumbnail" name="thumbnail"
                                           accept="image/jpeg,image/png,image/gif,image/webp">
                                </div>
                                <div id="imagePreview" class="mt-3 d-none">
                                    <img id="previewImg" src="" alt="معاينة الصورة" class="img-fluid rounded" style="max-height: 200px;">
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeImage">
                                            <i class="fas fa-trash me-1"></i>إزالة الصورة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Preview -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">👁️</div>
                                <h4 class="mb-0">معاينة الدورة</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="preview-card" id="coursePreview">
                                        <div class="preview-thumbnail" id="previewThumbnail">
                                            <i class="fas fa-image"></i>
                                        </div>
                                        <div class="preview-title" id="previewTitle">عنوان الدورة</div>
                                        <div class="preview-description" id="previewDescription">وصف الدورة سيظهر هنا...</div>
                                        <div class="preview-meta">
                                            <span id="previewLevel">مبتدئ</span>
                                            <span id="previewDuration">-- ساعة</span>
                                            <span id="previewPrice" class="fw-bold text-primary">مجاني</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="feature-highlight">
                                        <h6><i class="fas fa-eye me-2"></i>معاينة مباشرة</h6>
                                        <p>هذه هي الطريقة التي ستظهر بها دورتك للطلاب. تتحدث المعاينة تلقائياً مع كل تغيير تقوم به.</p>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="button" class="save-draft-btn" id="saveDraft">
                                            <i class="fas fa-save me-2"></i>حفظ كمسودة
                                        </button>
                                        <button type="button" class="btn btn-outline-info" id="previewFullscreen">
                                            <i class="fas fa-expand me-2"></i>معاينة كاملة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-gradient btn-lg px-5">
                                <i class="fas fa-rocket me-2"></i>إنشاء الدورة ونشرها
                            </button>
                            <button type="button" class="save-draft-btn btn-lg px-5 ms-3" id="saveDraftMain">
                                <i class="fas fa-save me-2"></i>حفظ كمسودة
                            </button>
                            <a href="courses.php" class="btn btn-outline-secondary btn-lg px-5 ms-3">
                                <i class="fas fa-arrow-left me-2"></i>العودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Floating Action Button -->
<button class="floating-action-btn" id="floatingHelp" data-bs-toggle="tooltip" title="مساعدة سريعة">
    <i class="fas fa-question"></i>
</button>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Progress tracking
let currentStep = 1;
const totalSteps = 4;

function updateProgress() {
    const progress = (currentStep / totalSteps) * 100;
    document.getElementById('progressBar').style.width = progress + '%';

    // Update step indicators
    document.querySelectorAll('.step-indicator-item').forEach((item, index) => {
        const stepNumber = index + 1;
        item.classList.remove('active', 'completed');

        if (stepNumber < currentStep) {
            item.classList.add('completed');
        } else if (stepNumber === currentStep) {
            item.classList.add('active');
        }
    });
}

// Form validation with progress tracking
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                event.preventDefault();
                if (form.checkValidity() === false) {
                    event.stopPropagation();
                    // Find first invalid field and scroll to it
                    const firstInvalid = form.querySelector(':invalid');
                    if (firstInvalid) {
                        firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstInvalid.focus();
                    }
                } else {
                    // Show success animation
                    showSuccessAnimation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

function showSuccessAnimation() {
    const button = document.querySelector('button[type="submit"]');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-check me-2"></i>تم إنشاء الدورة بنجاح!';
    button.classList.add('btn-success');
    button.classList.remove('btn-gradient');

    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-gradient');

        // Show detailed success message
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تهانينا!</strong> تم إنشاء دورتك بنجاح. يمكنك الآن إضافة الدروس وإدارة المحتوى.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        const formCard = document.querySelector('.form-card');
        formCard.insertAdjacentHTML('afterbegin', alertHtml);
        formCard.scrollIntoView({ behavior: 'smooth' });
    }, 2000);
}

// File upload functionality
const fileUploadArea = document.getElementById('fileUploadArea');
const fileInput = document.getElementById('thumbnail');
const imagePreview = document.getElementById('imagePreview');
const previewImg = document.getElementById('previewImg');
const removeImageBtn = document.getElementById('removeImage');

// Click to select file
fileUploadArea.addEventListener('click', () => {
    fileInput.click();
});

// Drag and drop functionality
fileUploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    fileUploadArea.style.borderColor = '#764ba2';
    fileUploadArea.style.background = 'rgba(102, 126, 234, 0.1)';
});

fileUploadArea.addEventListener('dragleave', () => {
    fileUploadArea.style.borderColor = '#667eea';
    fileUploadArea.style.background = 'transparent';
});

fileUploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    fileUploadArea.style.borderColor = '#667eea';
    fileUploadArea.style.background = 'transparent';

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect(files[0]);
    }
});

// File input change
fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        handleFileSelect(e.target.files[0]);
    }
});

// Handle file selection
function handleFileSelect(file) {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        alert('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF, أو WebP');
        return;
    }

    // Validate file size (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
        alert('حجم الملف كبير جداً. الحد الأقصى هو 5MB');
        return;
    }

    // Show preview
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImg.src = e.target.result;
        imagePreview.classList.remove('d-none');
        fileUploadArea.style.display = 'none';
    };
    reader.readAsDataURL(file);
}

// Remove image
removeImageBtn.addEventListener('click', () => {
    fileInput.value = '';
    imagePreview.classList.add('d-none');
    fileUploadArea.style.display = 'block';
});

// Price validation
const priceInput = document.getElementById('price');
const discountPriceInput = document.getElementById('discount_price');

discountPriceInput.addEventListener('input', function() {
    const price = parseFloat(priceInput.value) || 0;
    const discountPrice = parseFloat(this.value) || 0;

    if (discountPrice >= price && price > 0) {
        this.setCustomValidity('سعر الخصم يجب أن يكون أقل من السعر الأصلي');
    } else {
        this.setCustomValidity('');
    }
});

// Character counters
function setupCharacterCounter(inputId, counterId, maxLength) {
    const input = document.getElementById(inputId);
    const counter = document.getElementById(counterId);

    if (input && counter) {
        input.addEventListener('input', function() {
            const currentLength = this.value.length;
            const remaining = maxLength - currentLength;

            counter.textContent = `${currentLength}/${maxLength} حرف`;

            if (remaining < 50) {
                counter.classList.add('warning');
            } else {
                counter.classList.remove('warning');
            }
        });
    }
}

setupCharacterCounter('title', 'titleCounter', 100);
setupCharacterCounter('description', 'descriptionCounter', 1000);

// Learning objectives management
let objectiveCount = 1;
document.getElementById('addObjective').addEventListener('click', function() {
    if (objectiveCount < 8) {
        objectiveCount++;
        const objectivesList = document.getElementById('objectivesList');
        const newObjective = document.createElement('div');
        newObjective.className = 'input-group mb-2';
        newObjective.innerHTML = `
            <span class="input-group-addon">${objectiveCount}.</span>
            <input type="text" class="form-control" name="objectives[]"
                   placeholder="أدخل هدف تعليمي آخر">
            <button type="button" class="btn btn-outline-danger" onclick="removeObjective(this)">
                <i class="fas fa-times"></i>
            </button>
        `;
        objectivesList.appendChild(newObjective);

        // Animate new objective
        newObjective.style.opacity = '0';
        newObjective.style.transform = 'translateX(-20px)';
        setTimeout(() => {
            newObjective.style.transition = 'all 0.3s ease';
            newObjective.style.opacity = '1';
            newObjective.style.transform = 'translateX(0)';
        }, 10);
    }

    if (objectiveCount >= 8) {
        this.style.display = 'none';
    }
});

function removeObjective(button) {
    const objective = button.closest('.input-group');
    objective.style.transition = 'all 0.3s ease';
    objective.style.opacity = '0';
    objective.style.transform = 'translateX(-20px)';

    setTimeout(() => {
        objective.remove();
        objectiveCount--;

        // Renumber objectives
        document.querySelectorAll('#objectivesList .input-group-addon').forEach((addon, index) => {
            addon.textContent = (index + 1) + '.';
        });

        // Show add button if hidden
        if (objectiveCount < 8) {
            document.getElementById('addObjective').style.display = 'inline-block';
        }
    }, 300);
}

// Pricing type handler
document.getElementById('pricing_type').addEventListener('change', function() {
    const priceGroup = document.getElementById('priceGroup');
    const discountGroup = document.getElementById('discountGroup');
    const priceInput = document.getElementById('price');

    if (this.value === 'free') {
        priceGroup.style.display = 'none';
        discountGroup.style.display = 'none';
        priceInput.value = '0';
        priceInput.required = false;
    } else {
        priceGroup.style.display = 'block';
        discountGroup.style.display = 'block';
        priceInput.required = true;
    }

    updatePreview();
});

// Price suggestions
document.querySelectorAll('.price-suggestion').forEach(button => {
    button.addEventListener('click', function() {
        const price = this.dataset.price;
        document.getElementById('price').value = price;

        // Remove active class from all suggestions
        document.querySelectorAll('.price-suggestion').forEach(btn => {
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-secondary');
        });

        // Add active class to clicked suggestion
        this.classList.remove('btn-outline-secondary');
        this.classList.add('btn-primary');

        updatePreview();
    });
});

// Discount percentage calculation
function calculateDiscountPercentage() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const discountPrice = parseFloat(document.getElementById('discount_price').value) || 0;
    const percentageElement = document.getElementById('discountPercentage');

    if (price > 0 && discountPrice > 0 && discountPrice < price) {
        const percentage = Math.round(((price - discountPrice) / price) * 100);
        percentageElement.textContent = `خصم ${percentage}%`;
        percentageElement.style.display = 'block';
    } else {
        percentageElement.style.display = 'none';
    }
}

document.getElementById('price').addEventListener('input', calculateDiscountPercentage);
document.getElementById('discount_price').addEventListener('input', calculateDiscountPercentage);

// Live preview updates
function updatePreview() {
    const title = document.getElementById('title').value || 'عنوان الدورة';
    const description = document.getElementById('description').value || 'وصف الدورة سيظهر هنا...';
    const level = document.getElementById('level').selectedOptions[0].text;
    const duration = document.getElementById('duration').value;
    const price = document.getElementById('price').value;
    const discountPrice = document.getElementById('discount_price').value;
    const pricingType = document.getElementById('pricing_type').value;

    // Update preview elements
    document.getElementById('previewTitle').textContent = title;
    document.getElementById('previewDescription').textContent = description.substring(0, 150) + (description.length > 150 ? '...' : '');
    document.getElementById('previewLevel').textContent = level.split(' - ')[0];
    document.getElementById('previewDuration').textContent = duration ? duration + ' ساعة' : '-- ساعة';

    // Update price display
    const priceElement = document.getElementById('previewPrice');
    if (pricingType === 'free' || price == 0) {
        priceElement.textContent = 'مجاني';
        priceElement.className = 'fw-bold text-success';
    } else if (discountPrice && discountPrice < price) {
        priceElement.innerHTML = `<span class="text-decoration-line-through text-muted">${price} ريال</span> ${discountPrice} ريال`;
        priceElement.className = 'fw-bold text-danger';
    } else {
        priceElement.textContent = price + ' ريال';
        priceElement.className = 'fw-bold text-primary';
    }

    // Update preview card style
    const previewCard = document.getElementById('coursePreview');
    if (title !== 'عنوان الدورة' || description !== 'وصف الدورة سيظهر هنا...') {
        previewCard.classList.add('has-content');
    } else {
        previewCard.classList.remove('has-content');
    }
}

// Add event listeners for live preview
['title', 'description', 'level', 'duration', 'price', 'discount_price'].forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        element.addEventListener('input', updatePreview);
        element.addEventListener('change', updatePreview);
    }
});

// Smooth scrolling for form steps
document.querySelectorAll('.form-step').forEach((step, index) => {
    step.style.opacity = '0';
    step.style.transform = 'translateY(20px)';
    step.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

    setTimeout(() => {
        step.style.opacity = '1';
        step.style.transform = 'translateY(0)';
    }, index * 200);
});

// Save draft functionality
document.getElementById('saveDraft').addEventListener('click', function() {
    const button = this;
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    button.disabled = true;

    setTimeout(() => {
        button.innerHTML = '<i class="fas fa-check me-2"></i>تم الحفظ';
        button.classList.add('btn-success');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.disabled = false;
        }, 2000);
    }, 1500);
});

document.getElementById('saveDraftMain').addEventListener('click', function() {
    document.getElementById('saveDraft').click();
});

// Floating help button
document.getElementById('floatingHelp').addEventListener('click', function() {
    const helpContent = `
        <div class="modal fade" id="helpModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">مساعدة سريعة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-lightbulb text-warning me-2"></i>نصائح للنجاح</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">✅ اختر عنواناً واضحاً وجذاباً</li>
                                    <li class="mb-2">✅ اكتب وصفاً شاملاً ومفصلاً</li>
                                    <li class="mb-2">✅ حدد السعر المناسب للمحتوى</li>
                                    <li class="mb-2">✅ استخدم صورة عالية الجودة</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-keyboard text-info me-2"></i>اختصارات لوحة المفاتيح</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2"><kbd>Ctrl + S</kbd> حفظ كمسودة</li>
                                    <li class="mb-2"><kbd>Ctrl + Enter</kbd> إنشاء الدورة</li>
                                    <li class="mb-2"><kbd>Esc</kbd> إغلاق النوافذ</li>
                                    <li class="mb-2"><kbd>Tab</kbd> الانتقال بين الحقول</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('helpModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', helpContent);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('helpModal'));
    modal.show();
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl + S for save draft
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        document.getElementById('saveDraft').click();
    }

    // Ctrl + Enter for submit
    if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        document.querySelector('button[type="submit"]').click();
    }

    // Esc to close modals
    if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
            const modal = bootstrap.Modal.getInstance(openModal);
            if (modal) modal.hide();
        }
    }
});

// Auto-save functionality (every 30 seconds)
let autoSaveInterval;
function startAutoSave() {
    autoSaveInterval = setInterval(() => {
        const title = document.getElementById('title').value;
        const description = document.getElementById('description').value;

        if (title.trim() || description.trim()) {
            // Show subtle auto-save indicator
            const indicator = document.createElement('div');
            indicator.className = 'position-fixed top-0 end-0 m-3 alert alert-info alert-dismissible fade show';
            indicator.style.zIndex = '9999';
            indicator.innerHTML = `
                <i class="fas fa-cloud me-2"></i>تم الحفظ التلقائي
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(indicator);

            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.remove();
                }
            }, 3000);
        }
    }, 30000); // 30 seconds
}

// Start auto-save when user starts typing
let hasStartedTyping = false;
document.addEventListener('input', function() {
    if (!hasStartedTyping) {
        hasStartedTyping = true;
        startAutoSave();
    }
});

// Advanced options toggle animation
document.querySelector('.advanced-toggle').addEventListener('click', function() {
    this.classList.toggle('collapsed');
});

// Form step tracking based on scroll position
function trackFormProgress() {
    const steps = document.querySelectorAll('.form-step');
    const scrollPosition = window.scrollY + window.innerHeight / 2;

    steps.forEach((step, index) => {
        const stepTop = step.offsetTop;
        const stepBottom = stepTop + step.offsetHeight;

        if (scrollPosition >= stepTop && scrollPosition <= stepBottom) {
            if (currentStep !== index + 1) {
                currentStep = index + 1;
                updateProgress();
            }
        }
    });
}

window.addEventListener('scroll', trackFormProgress);

// Initialize everything
updateProgress();
updatePreview();

// Welcome animation
setTimeout(() => {
    const welcomeAlert = document.createElement('div');
    welcomeAlert.className = 'position-fixed bottom-0 end-0 m-3 alert alert-success alert-dismissible fade show';
    welcomeAlert.style.zIndex = '9999';
    welcomeAlert.innerHTML = `
        <i class="fas fa-rocket me-2"></i>مرحباً! ابدأ بإنشاء دورتك الرائعة
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(welcomeAlert);

    setTimeout(() => {
        if (welcomeAlert.parentNode) {
            welcomeAlert.remove();
        }
    }, 5000);
}, 1000);
</script>

</body>
</html>
