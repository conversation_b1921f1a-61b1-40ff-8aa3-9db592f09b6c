<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة دورة جديدة - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
            --shadow-medium: 0 10px 30px rgba(0,0,0,0.1);
            --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
            --border-radius: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: var(--shadow-medium);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: white !important;
        }

        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            margin: 0 5px;
            padding: 8px 16px !important;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white !important;
            transform: translateY(-1px);
        }

        .hero-section {
            background: var(--primary-gradient);
            color: white;
            padding: 3rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .form-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-heavy);
            padding: 2.5rem;
            margin-top: -2rem;
            position: relative;
            z-index: 2;
            border: none;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            transition: var(--transition);
            font-size: 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-gradient:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .form-step {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-light);
            border-left: 4px solid #667eea;
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-gradient);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 1rem;
        }

        .file-upload-area {
            border: 2px dashed #667eea;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.05);
        }

        .breadcrumb {
            background: transparent;
            padding: 1rem 0;
        }

        .breadcrumb-item a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: white;
        }

        .price-input-group {
            position: relative;
        }

        .price-input-group .form-control {
            padding-right: 50px;
        }

        .price-currency {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #718096;
            font-weight: 600;
        }
    </style>
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="../index.php">
            <i class="fas fa-graduation-cap me-2"></i>
            منصة التعليم التفاعلي
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">لوحة التحكم</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="courses.php">دوراتي</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="add-course.php">إضافة دورة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="students.php">الطلاب</a>
                </li>
            </ul>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn btn-link text-white text-decoration-none dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2"></i>
                        مدرب تجريبي
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i> الملف الشخصي
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="dashboard.php">لوحة التحكم</a></li>
                <li class="breadcrumb-item active">إضافة دورة جديدة</li>
            </ol>
        </nav>
        
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-3">إضافة دورة جديدة</h1>
                <p class="lead mb-0">أنشئ دورة تعليمية جديدة وشاركها مع الطلاب</p>
            </div>
            <div class="col-md-4 text-center">
                <i class="fas fa-plus-circle" style="font-size: 5rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Add Course Form -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-card">
                    <!-- Demo Alert -->
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>هذه نسخة تجريبية من صفحة إضافة الدورة. التصميم يعمل بشكل كامل!
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>

                    <form method="POST" action="" enctype="multipart/form-data" class="needs-validation" novalidate>
                        <!-- Step 1: Basic Information -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">1</div>
                                <h4 class="mb-0">المعلومات الأساسية</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="title" class="form-label">عنوان الدورة *</label>
                                    <input type="text" class="form-control" id="title" name="title"
                                           placeholder="أدخل عنوان الدورة" required>
                                    <div class="invalid-feedback">يرجى إدخال عنوان الدورة</div>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="category_id" class="form-label">الفئة *</label>
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="1">البرمجة</option>
                                        <option value="2">التصميم</option>
                                        <option value="3">التسويق</option>
                                        <option value="4">الأعمال</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار فئة الدورة</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">وصف الدورة *</label>
                                <textarea class="form-control" id="description" name="description" rows="5"
                                          placeholder="اكتب وصفاً شاملاً للدورة وما سيتعلمه الطلاب" required></textarea>
                                <div class="invalid-feedback">يرجى إدخال وصف الدورة</div>
                            </div>
                        </div>

                        <!-- Step 2: Course Details -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">2</div>
                                <h4 class="mb-0">تفاصيل الدورة</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="level" class="form-label">مستوى الدورة</label>
                                    <select class="form-select" id="level" name="level">
                                        <option value="beginner">مبتدئ</option>
                                        <option value="intermediate">متوسط</option>
                                        <option value="advanced">متقدم</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="language" class="form-label">لغة الدورة</label>
                                    <select class="form-select" id="language" name="language">
                                        <option value="Arabic">العربية</option>
                                        <option value="English">الإنجليزية</option>
                                        <option value="French">الفرنسية</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="duration" class="form-label">مدة الدورة (بالدقائق)</label>
                                    <input type="number" class="form-control" id="duration" name="duration"
                                           placeholder="مثال: 120" min="1">
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Pricing -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">3</div>
                                <h4 class="mb-0">التسعير</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="price" class="form-label">سعر الدورة *</label>
                                    <div class="price-input-group">
                                        <input type="number" class="form-control" id="price" name="price"
                                               placeholder="0.00" min="0" step="0.01" required>
                                        <span class="price-currency">ريال</span>
                                    </div>
                                    <small class="text-muted">أدخل 0 للدورات المجانية</small>
                                    <div class="invalid-feedback">يرجى إدخال سعر الدورة</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="discount_price" class="form-label">سعر الخصم (اختياري)</label>
                                    <div class="price-input-group">
                                        <input type="number" class="form-control" id="discount_price" name="discount_price"
                                               placeholder="0.00" min="0" step="0.01">
                                        <span class="price-currency">ريال</span>
                                    </div>
                                    <small class="text-muted">يجب أن يكون أقل من السعر الأصلي</small>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4: Course Thumbnail -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">4</div>
                                <h4 class="mb-0">صورة الدورة</h4>
                            </div>

                            <div class="mb-3">
                                <label for="thumbnail" class="form-label">صورة مصغرة للدورة</label>
                                <div class="file-upload-area" id="fileUploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h5>اسحب وأفلت الصورة هنا أو انقر للاختيار</h5>
                                    <p class="text-muted mb-0">الحد الأقصى: 5MB | الأنواع المدعومة: JPG, PNG, GIF, WebP</p>
                                    <input type="file" class="form-control d-none" id="thumbnail" name="thumbnail"
                                           accept="image/jpeg,image/png,image/gif,image/webp">
                                </div>
                                <div id="imagePreview" class="mt-3 d-none">
                                    <img id="previewImg" src="" alt="معاينة الصورة" class="img-fluid rounded" style="max-height: 200px;">
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeImage">
                                            <i class="fas fa-trash me-1"></i>إزالة الصورة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-gradient btn-lg px-5">
                                <i class="fas fa-plus me-2"></i>إنشاء الدورة
                            </button>
                            <a href="courses.php" class="btn btn-outline-secondary btn-lg px-5 ms-3">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                event.preventDefault(); // منع الإرسال الفعلي في النسخة التجريبية
                if (form.checkValidity() === false) {
                    event.stopPropagation();
                } else {
                    // عرض رسالة نجاح تجريبية
                    alert('تم إنشاء الدورة بنجاح! (هذه نسخة تجريبية)');
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// File upload functionality
const fileUploadArea = document.getElementById('fileUploadArea');
const fileInput = document.getElementById('thumbnail');
const imagePreview = document.getElementById('imagePreview');
const previewImg = document.getElementById('previewImg');
const removeImageBtn = document.getElementById('removeImage');

// Click to select file
fileUploadArea.addEventListener('click', () => {
    fileInput.click();
});

// Drag and drop functionality
fileUploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    fileUploadArea.style.borderColor = '#764ba2';
    fileUploadArea.style.background = 'rgba(102, 126, 234, 0.1)';
});

fileUploadArea.addEventListener('dragleave', () => {
    fileUploadArea.style.borderColor = '#667eea';
    fileUploadArea.style.background = 'transparent';
});

fileUploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    fileUploadArea.style.borderColor = '#667eea';
    fileUploadArea.style.background = 'transparent';

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect(files[0]);
    }
});

// File input change
fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        handleFileSelect(e.target.files[0]);
    }
});

// Handle file selection
function handleFileSelect(file) {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        alert('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF, أو WebP');
        return;
    }

    // Validate file size (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
        alert('حجم الملف كبير جداً. الحد الأقصى هو 5MB');
        return;
    }

    // Show preview
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImg.src = e.target.result;
        imagePreview.classList.remove('d-none');
        fileUploadArea.style.display = 'none';
    };
    reader.readAsDataURL(file);
}

// Remove image
removeImageBtn.addEventListener('click', () => {
    fileInput.value = '';
    imagePreview.classList.add('d-none');
    fileUploadArea.style.display = 'block';
});

// Price validation
const priceInput = document.getElementById('price');
const discountPriceInput = document.getElementById('discount_price');

discountPriceInput.addEventListener('input', function() {
    const price = parseFloat(priceInput.value) || 0;
    const discountPrice = parseFloat(this.value) || 0;

    if (discountPrice >= price && price > 0) {
        this.setCustomValidity('سعر الخصم يجب أن يكون أقل من السعر الأصلي');
    } else {
        this.setCustomValidity('');
    }
});

// Smooth scrolling for form steps
document.querySelectorAll('.form-step').forEach((step, index) => {
    step.style.opacity = '0';
    step.style.transform = 'translateY(20px)';
    step.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

    setTimeout(() => {
        step.style.opacity = '1';
        step.style.transform = 'translateY(0)';
    }, index * 200);
});
</script>

</body>
</html>
