/* General Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --background-light: #f8f9fa;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0a58ca 100%);
    color: white;
    padding: 100px 0;
}

.hero-section h1 {
    font-weight: 700;
}

/* Cards */
.card {
    transition: transform 0.3s ease;
    border: none;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-5px);
}

/* Categories Section */
.categories .card {
    text-align: center;
    padding: 2rem;
}

.categories .fas {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Featured Courses */
.featured-courses .card {
    border-radius: 10px;
    overflow: hidden;
}

.featured-courses .card-img-top {
    height: 200px;
    object-fit: cover;
}

/* Testimonials */
.testimonials .card {
    border-radius: 10px;
}

.testimonials img {
    width: 50px;
    height: 50px;
    object-fit: cover;
}

/* Footer */
footer {
    background-color: #212529;
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--primary-color) !important;
}

.social-links a {
    font-size: 1.5rem;
    margin-right: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 50px 0;
    }
    
    .hero-section img {
        margin-top: 2rem;
    }
}

/* Custom Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Course Card Styles */
.course-card {
    position: relative;
    overflow: hidden;
}

.course-card .course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.course-card:hover .course-overlay {
    opacity: 1;
}

/* Button Styles */
.btn-primary {
    padding: 0.8rem 2rem;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-outline-primary {
    border-width: 2px;
    font-weight: 600;
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* Search Bar */
.search-bar {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-bar input {
    padding-right: 50px;
    border-radius: 30px;
}

.search-bar button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: none;
    color: var(--primary-color);
}

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} 