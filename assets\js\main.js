// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Search functionality
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchQuery = this.querySelector('input').value;
            // Implement search functionality
            console.log('Searching for:', searchQuery);
        });
    }

    // Course filtering
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            filterCourses(category);
        });
    });

    // Course rating system
    const ratingStars = document.querySelectorAll('.rating-star');
    ratingStars.forEach(star => {
        star.addEventListener('click', function() {
            const rating = this.dataset.rating;
            updateRating(rating);
        });
    });

    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.navbar-toggler');
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            document.querySelector('.navbar-collapse').classList.toggle('show');
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Course progress tracking
    function updateCourseProgress(courseId, progress) {
        // Implement progress tracking
        console.log(`Updating progress for course ${courseId}: ${progress}%`);
    }

    // Video player controls
    const videoPlayers = document.querySelectorAll('.course-video');
    videoPlayers.forEach(player => {
        player.addEventListener('play', function() {
            // Track video progress
            console.log('Video started playing');
        });
    });

    // Payment integration
    function initializePayment(amount, currency) {
        // Implement payment gateway integration
        console.log(`Processing payment: ${amount} ${currency}`);
    }

    // Certificate generation
    function generateCertificate(courseId, userId) {
        // Implement certificate generation
        console.log(`Generating certificate for course ${courseId} and user ${userId}`);
    }

    // Notifications system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} notification`;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Dark mode toggle
    const darkModeToggle = document.querySelector('.dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });
    }

    // Check for saved dark mode preference
    if (localStorage.getItem('darkMode') === 'true') {
        document.body.classList.add('dark-mode');
    }

    // Course enrollment
    function enrollInCourse(courseId) {
        // Implement course enrollment
        console.log(`Enrolling in course ${courseId}`);
    }

    // Course bookmarking
    function toggleBookmark(courseId) {
        // Implement course bookmarking
        console.log(`Toggling bookmark for course ${courseId}`);
    }

    // Export functions to global scope if needed
    window.showNotification = showNotification;
    window.enrollInCourse = enrollInCourse;
    window.toggleBookmark = toggleBookmark;
}); 