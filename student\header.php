<?php
// Get current page for active menu item
$current_page = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . APP_NAME : APP_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <header class="bg-white shadow-sm">
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container">
                <a class="navbar-brand" href="../index.php">
                    <img src="../assets/images/logo.png" alt="<?php echo APP_NAME; ?>" height="40" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <span style="display:none;"><?php echo APP_NAME; ?></span>
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="../index.php">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../courses.php">الدورات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../instructors.php">المدرسون</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">لوحة التحكم</a>
                        </li>
                    </ul>
                    
                    <div class="d-flex align-items-center">
                        <?php if (isAuthenticated()): ?>
                            <!-- User Menu -->
                            <div class="dropdown">
                                <button class="btn btn-link text-dark text-decoration-none dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <img src="<?php echo $_SESSION['user_image'] ?? '../assets/images/avatar-placeholder.jpg'; ?>" 
                                         class="rounded-circle me-2" width="32" height="32" alt="<?php echo $_SESSION['user_name']; ?>"
                                         onerror="this.src='../assets/images/avatar-placeholder.jpg'">
                                    <?php echo $_SESSION['user_name']; ?>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item" href="dashboard.php">
                                            <i class="fas fa-user-graduate me-2"></i> لوحة الطالب
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="profile.php">
                                            <i class="fas fa-user me-2"></i> الملف الشخصي
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="settings.php">
                                            <i class="fas fa-cog me-2"></i> الإعدادات
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="../logout.php">
                                            <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        <?php else: ?>
                            <!-- Auth Buttons -->
                            <a href="../login.php" class="btn btn-outline-primary me-2">تسجيل الدخول</a>
                            <a href="../register.php" class="btn btn-primary">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>
