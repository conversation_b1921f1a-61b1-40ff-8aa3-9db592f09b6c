<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .setup-container {
            max-width: 800px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .setup-content {
            padding: 2rem;
        }
        .btn-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-setup:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        .status-message {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            display: none;
        }
        .status-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 1rem 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list i {
            color: #667eea;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-database me-3"></i>إعداد قاعدة البيانات</h1>
            <p class="mb-0">تهيئة قاعدة البيانات مع البيانات التجريبية</p>
        </div>
        
        <div class="setup-content">
            <div class="row">
                <div class="col-md-6">
                    <h4><i class="fas fa-list-check me-2"></i>ما سيتم إنشاؤه:</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-table"></i> جدول النشرة الإخبارية</li>
                        <li><i class="fas fa-star"></i> جدول شهادات الطلاب</li>
                        <li><i class="fas fa-chart-bar"></i> جدول إحصائيات الموقع</li>
                        <li><i class="fas fa-graduation-cap"></i> 6 دورات تجريبية</li>
                        <li><i class="fas fa-user-tie"></i> 6 مدربين تجريبيين</li>
                        <li><i class="fas fa-tags"></i> 8 فئات تعليمية</li>
                        <li><i class="fas fa-comments"></i> 3 شهادات طلاب</li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <h4><i class="fas fa-rocket me-2"></i>الميزات المضافة:</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-database"></i> ربط كامل بقاعدة البيانات</li>
                        <li><i class="fas fa-envelope"></i> نشرة إخبارية تفاعلية</li>
                        <li><i class="fas fa-chart-line"></i> إحصائيات حقيقية</li>
                        <li><i class="fas fa-heart"></i> قائمة أمنيات تعمل</li>
                        <li><i class="fas fa-search"></i> بحث متقدم</li>
                        <li><i class="fas fa-mobile-alt"></i> تصميم متجاوب</li>
                        <li><i class="fas fa-shield-alt"></i> حماية من الأخطاء</li>
                    </ul>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <button class="btn btn-setup btn-lg" onclick="setupDatabase()">
                    <i class="fas fa-play me-2"></i>بدء إعداد قاعدة البيانات
                </button>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري إعداد قاعدة البيانات...</p>
            </div>
            
            <div class="status-message" id="statusMessage"></div>
            
            <div class="mt-4 text-center">
                <a href="index.php" class="btn btn-outline-primary">
                    <i class="fas fa-home me-2"></i>عرض الصفحة الرئيسية
                </a>
                <a href="admin/" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-cog me-2"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function setupDatabase() {
            const button = document.querySelector('.btn-setup');
            const loading = document.getElementById('loading');
            const statusMessage = document.getElementById('statusMessage');
            
            // Show loading
            button.style.display = 'none';
            loading.style.display = 'block';
            statusMessage.style.display = 'none';
            
            // Make AJAX request to setup_database.php
            fetch('setup_database.php')
                .then(response => response.text())
                .then(data => {
                    loading.style.display = 'none';
                    statusMessage.style.display = 'block';
                    
                    if (data.includes('✅') || data.includes('successfully')) {
                        statusMessage.className = 'status-message status-success';
                        statusMessage.innerHTML = `
                            <h5><i class="fas fa-check-circle me-2"></i>تم إعداد قاعدة البيانات بنجاح!</h5>
                            <p>تم إنشاء جميع الجداول وإدراج البيانات التجريبية.</p>
                            <div class="mt-3">
                                <a href="index.php" class="btn btn-success me-2">
                                    <i class="fas fa-eye me-2"></i>عرض الصفحة الرئيسية
                                </a>
                                <a href="courses.php" class="btn btn-info">
                                    <i class="fas fa-graduation-cap me-2"></i>عرض الدورات
                                </a>
                            </div>
                        `;
                    } else {
                        statusMessage.className = 'status-message status-error';
                        statusMessage.innerHTML = `
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>حدث خطأ أثناء الإعداد</h5>
                            <p>يرجى التحقق من إعدادات قاعدة البيانات والمحاولة مرة أخرى.</p>
                            <details class="mt-2">
                                <summary>تفاصيل الخطأ</summary>
                                <pre class="mt-2 p-2 bg-light border rounded">${data}</pre>
                            </details>
                        `;
                        button.style.display = 'inline-block';
                    }
                })
                .catch(error => {
                    loading.style.display = 'none';
                    statusMessage.style.display = 'block';
                    statusMessage.className = 'status-message status-error';
                    statusMessage.innerHTML = `
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>خطأ في الاتصال</h5>
                        <p>تعذر الاتصال بالخادم. يرجى التأكد من تشغيل الخادم المحلي.</p>
                        <p class="small text-muted">خطأ: ${error.message}</p>
                    `;
                    button.style.display = 'inline-block';
                });
        }
        
        // Auto-setup on page load (optional)
        // setupDatabase();
    </script>
</body>
</html>
