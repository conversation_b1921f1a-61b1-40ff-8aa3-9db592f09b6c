<?php
require_once 'config/config.php';
require_once 'config/database_auto.php';

// Get course ID from URL
$course_id = isset($_GET['id']) ? (int)$_GET['id'] : 1; // Default to course 1 for demo

// Sample course data (since database might not be set up)
$sample_courses = [
    1 => [
        'id' => 1,
        'title' => 'تطوير المواقع باستخدام HTML و CSS',
        'description' => 'تعلم أساسيات تطوير المواقع من الصفر باستخدام HTML و CSS مع مشاريع عملية ومتقدمة. ستتعلم كيفية إنشاء مواقع ويب جميلة ومتجاوبة.',
        'thumbnail' => 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=600&h=400&fit=crop&crop=center',
        'price' => 0,
        'level' => 'beginner',
        'category_name' => 'تطوير الويب',
        'instructor_name' => 'أحمد محمد',
        'instructor_avatar' => 'https://ui-avatars.com/api/?name=أحمد+محمد&background=667eea&color=fff&size=100',
        'rating' => 4.8,
        'enrolled_students' => 150,
        'duration' => 20,
        'total_reviews' => 45,
        'created_at' => date('Y-m-d H:i:s')
    ],
    2 => [
        'id' => 2,
        'title' => 'البرمجة بلغة JavaScript',
        'description' => 'دورة شاملة لتعلم JavaScript من المبتدئ إلى المحترف مع التطبيقات العملية والمشاريع الحقيقية. ستتعلم ES6+ والبرمجة الحديثة.',
        'thumbnail' => 'https://images.unsplash.com/photo-1579468118864-1b9ea3c0db4a?w=600&h=400&fit=crop&crop=center',
        'price' => 299,
        'level' => 'intermediate',
        'category_name' => 'البرمجة',
        'instructor_name' => 'فاطمة علي',
        'instructor_avatar' => 'https://ui-avatars.com/api/?name=فاطمة+علي&background=f093fb&color=fff&size=100',
        'rating' => 4.9,
        'enrolled_students' => 200,
        'duration' => 35,
        'total_reviews' => 67,
        'created_at' => date('Y-m-d H:i:s')
    ]
];

// Try to get course from database, fallback to sample data
try {
    $course = fetch("
        SELECT
            c.*,
            u.full_name as instructor_name,
            u.avatar as instructor_avatar,
            cat.name as category_name
        FROM courses c
        LEFT JOIN users u ON c.instructor_id = u.id
        LEFT JOIN categories cat ON c.category_id = cat.id
        WHERE c.id = ? AND c.status = 'published'
    ", [$course_id]);

    if (!$course && isset($sample_courses[$course_id])) {
        $course = $sample_courses[$course_id];
    }
} catch (Exception $e) {
    $course = isset($sample_courses[$course_id]) ? $sample_courses[$course_id] : $sample_courses[1];
}

if (!$course) {
    header('Location: courses.php');
    exit;
}

// Sample curriculum data
$curriculum = [
    [
        'section_title' => 'مقدمة في HTML',
        'section_order' => 1,
        'title' => 'ما هو HTML؟',
        'duration' => 15,
        'lesson_order' => 1
    ],
    [
        'section_title' => 'مقدمة في HTML',
        'section_order' => 1,
        'title' => 'هيكل صفحة HTML',
        'duration' => 20,
        'lesson_order' => 2
    ],
    [
        'section_title' => 'مقدمة في HTML',
        'section_order' => 1,
        'title' => 'العناصر والخصائص',
        'duration' => 25,
        'lesson_order' => 3
    ],
    [
        'section_title' => 'أساسيات CSS',
        'section_order' => 2,
        'title' => 'مقدمة في CSS',
        'duration' => 18,
        'lesson_order' => 1
    ],
    [
        'section_title' => 'أساسيات CSS',
        'section_order' => 2,
        'title' => 'التحكم في الألوان والخطوط',
        'duration' => 22,
        'lesson_order' => 2
    ],
    [
        'section_title' => 'التصميم المتجاوب',
        'section_order' => 3,
        'title' => 'مقدمة في Responsive Design',
        'duration' => 30,
        'lesson_order' => 1
    ]
];

// Handle enrollment
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['enroll'])) {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
        exit;
    }

    // Simulate enrollment process
    $success = 'تم تسجيلك في الدورة بنجاح! يمكنك الآن البدء في التعلم.';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($course['title']); ?> - منصة التعليم التفاعلي</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }

        .course-thumbnail {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .course-thumbnail:hover {
            transform: translateY(-5px);
        }

        .instructor-avatar {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255,255,255,0.3);
        }

        .stats-item {
            background: rgba(255,255,255,0.1);
            padding: 10px 15px;
            border-radius: 25px;
            margin: 5px;
        }

        .content-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .content-card:hover {
            transform: translateY(-2px);
        }

        .lesson-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }

        .lesson-item:hover {
            background-color: #f8f9fa;
        }

        .lesson-item:last-child {
            border-bottom: none;
        }

        .price-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
        }

        .enroll-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .enroll-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .rating-stars {
            color: #ffc107;
        }
    </style>
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <a class="navbar-brand fw-bold" href="index.php">
            <i class="fas fa-graduation-cap me-2"></i>
            منصة التعليم التفاعلي
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="index.php">الرئيسية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="courses.php">الدورات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#about">من نحن</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#contact">اتصل بنا</a>
                </li>
            </ul>

            <div class="d-flex">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <a href="student/dashboard.php" class="btn btn-outline-light me-2">لوحة التحكم</a>
                    <a href="logout.php" class="btn btn-light">تسجيل الخروج</a>
                <?php else: ?>
                    <a href="login.php" class="btn btn-outline-light me-2">تسجيل الدخول</a>
                    <a href="register.php" class="btn btn-light">إنشاء حساب</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>

<!-- Course Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4"><?php echo htmlspecialchars($course['title']); ?></h1>
                <p class="lead mb-4"><?php echo htmlspecialchars($course['description']); ?></p>

                <div class="d-flex align-items-center mb-4">
                    <img src="<?php echo $course['instructor_avatar']; ?>"
                         alt="<?php echo htmlspecialchars($course['instructor_name']); ?>"
                         class="rounded-circle me-3 instructor-avatar"
                         style="object-fit: cover;">
                    <div>
                        <h5 class="mb-0"><?php echo htmlspecialchars($course['instructor_name']); ?></h5>
                        <small class="opacity-75">مدرس معتمد</small>
                    </div>
                </div>

                <div class="d-flex flex-wrap">
                    <?php if (isset($course['rating']) && $course['rating']): ?>
                        <div class="stats-item">
                            <i class="fas fa-star rating-stars"></i>
                            <?php echo number_format($course['rating'], 1); ?>
                            <small>(<?php echo $course['total_reviews'] ?? 0; ?> تقييم)</small>
                        </div>
                    <?php endif; ?>
                    <div class="stats-item">
                        <i class="fas fa-users"></i>
                        <?php echo $course['enrolled_students']; ?> طالب
                    </div>
                    <div class="stats-item">
                        <i class="fas fa-clock"></i>
                        <?php echo $course['duration']; ?> ساعة
                    </div>
                    <div class="stats-item">
                        <i class="fas fa-signal"></i>
                        <?php echo $course['level'] == 'beginner' ? 'مبتدئ' : ($course['level'] == 'intermediate' ? 'متوسط' : 'متقدم'); ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <img src="<?php echo $course['thumbnail']; ?>"
                     alt="<?php echo htmlspecialchars($course['title']); ?>"
                     class="img-fluid course-thumbnail">
            </div>
        </div>
    </div>
</section>

<!-- Course Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Course Details -->
            <div class="col-lg-8">
                <!-- Course Content -->
                <div class="content-card mb-4">
                    <div class="card-body">
                        <h3 class="card-title mb-4">
                            <i class="fas fa-list-ul me-2 text-primary"></i>
                            محتوى الدورة
                        </h3>

                        <?php
                        $current_section = null;
                        $section_lessons = [];

                        // Group lessons by section
                        foreach ($curriculum as $lesson) {
                            $section_lessons[$lesson['section_title']][] = $lesson;
                        }

                        foreach ($section_lessons as $section_title => $lessons):
                        ?>
                            <div class="mb-4">
                                <div class="section-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-folder-open me-2"></i>
                                        <?php echo htmlspecialchars($section_title); ?>
                                        <span class="badge bg-light text-dark ms-2"><?php echo count($lessons); ?> دروس</span>
                                    </h5>
                                </div>

                                <div class="bg-white rounded">
                                    <?php foreach ($lessons as $lesson): ?>
                                        <div class="lesson-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-play-circle text-primary me-3" style="font-size: 1.2em;"></i>
                                                    <div>
                                                        <h6 class="mb-0"><?php echo htmlspecialchars($lesson['title']); ?></h6>
                                                        <small class="text-muted">الدرس <?php echo $lesson['lesson_order']; ?></small>
                                                    </div>
                                                </div>
                                                <div class="text-end">
                                                    <span class="badge bg-light text-dark">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?php echo $lesson['duration']; ?> دقيقة
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Course Description -->
                <div class="content-card mb-4">
                    <div class="card-body">
                        <h3 class="card-title mb-4">
                            <i class="fas fa-info-circle me-2 text-primary"></i>
                            وصف الدورة
                        </h3>
                        <p class="lead"><?php echo htmlspecialchars($course['description']); ?></p>

                        <h5 class="mt-4 mb-3">ماذا ستتعلم في هذه الدورة؟</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> أساسيات HTML وهيكلة المحتوى</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> تنسيق الصفحات باستخدام CSS</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> إنشاء تصاميم متجاوبة</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> أفضل الممارسات في تطوير الويب</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> مشاريع عملية وتطبيقية</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Course Sidebar -->
            <div class="col-lg-4">
                <div class="sticky-top" style="top: 20px;">
                    <!-- Price Card -->
                    <div class="price-card mb-4">
                        <div class="text-center mb-4">
                            <?php if ($course['price'] > 0): ?>
                                <h2 class="mb-0">
                                    <?php echo number_format($course['price'], 0); ?> ريال
                                </h2>
                                <small class="opacity-75">سعر الدورة</small>
                            <?php else: ?>
                                <h2 class="mb-0">مجاني</h2>
                                <small class="opacity-75">دورة مجانية بالكامل</small>
                            <?php endif; ?>
                        </div>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="">
                            <button type="submit" name="enroll" class="enroll-btn w-100 mb-3">
                                <i class="fas fa-graduation-cap me-2"></i>
                                <?php echo $course['price'] > 0 ? 'اشترك الآن' : 'ابدأ التعلم مجاناً'; ?>
                            </button>
                        </form>

                        <ul class="feature-list list-unstyled">
                            <li>
                                <i class="fas fa-video me-2"></i>
                                <?php echo count($curriculum); ?> درس فيديو
                            </li>
                            <li>
                                <i class="fas fa-clock me-2"></i>
                                <?php echo $course['duration']; ?> ساعة من المحتوى
                            </li>
                            <li>
                                <i class="fas fa-download me-2"></i>
                                موارد قابلة للتحميل
                            </li>
                            <li>
                                <i class="fas fa-mobile-alt me-2"></i>
                                متاح على الهاتف المحمول
                            </li>
                            <li>
                                <i class="fas fa-certificate me-2"></i>
                                شهادة إتمام معتمدة
                            </li>
                            <li>
                                <i class="fas fa-infinity me-2"></i>
                                وصول مدى الحياة
                            </li>
                        </ul>
                    </div>

                    <!-- Course Stats -->
                    <div class="content-card">
                        <div class="card-body">
                            <h5 class="card-title mb-3">
                                <i class="fas fa-chart-bar me-2 text-primary"></i>
                                إحصائيات الدورة
                            </h5>

                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <div class="border-end">
                                        <h4 class="text-primary mb-0"><?php echo $course['enrolled_students']; ?></h4>
                                        <small class="text-muted">طالب مسجل</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <h4 class="text-warning mb-0">
                                        <?php echo isset($course['rating']) ? number_format($course['rating'], 1) : '4.8'; ?>
                                        <i class="fas fa-star"></i>
                                    </h4>
                                    <small class="text-muted">تقييم الدورة</small>
                                </div>
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-success mb-0"><?php echo count($curriculum); ?></h4>
                                        <small class="text-muted">درس</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-info mb-0"><?php echo $course['duration']; ?></h4>
                                    <small class="text-muted">ساعة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 mb-4">
                <h5 class="mb-3">
                    <i class="fas fa-graduation-cap me-2"></i>
                    منصة التعليم التفاعلي
                </h5>
                <p class="text-light">منصة تعليمية متقدمة تهدف إلى توفير أفضل تجربة تعلم تفاعلية للطلاب في العالم العربي.</p>
            </div>
            <div class="col-lg-2 mb-4">
                <h6 class="mb-3">روابط سريعة</h6>
                <ul class="list-unstyled">
                    <li><a href="index.php" class="text-light text-decoration-none">الرئيسية</a></li>
                    <li><a href="courses.php" class="text-light text-decoration-none">الدورات</a></li>
                    <li><a href="#" class="text-light text-decoration-none">من نحن</a></li>
                    <li><a href="#" class="text-light text-decoration-none">اتصل بنا</a></li>
                </ul>
            </div>
            <div class="col-lg-3 mb-4">
                <h6 class="mb-3">معلومات التواصل</h6>
                <p class="text-light mb-2">
                    <i class="fas fa-envelope me-2"></i>
                    <EMAIL>
                </p>
                <p class="text-light mb-2">
                    <i class="fas fa-phone me-2"></i>
                    +966 123 456 789
                </p>
            </div>
            <div class="col-lg-3 mb-4">
                <h6 class="mb-3">تابعنا على</h6>
                <div class="d-flex">
                    <a href="#" class="text-light me-3"><i class="fab fa-facebook fa-lg"></i></a>
                    <a href="#" class="text-light me-3"><i class="fab fa-twitter fa-lg"></i></a>
                    <a href="#" class="text-light me-3"><i class="fab fa-instagram fa-lg"></i></a>
                    <a href="#" class="text-light"><i class="fab fa-linkedin fa-lg"></i></a>
                </div>
            </div>
        </div>
        <hr class="my-4">
        <div class="text-center">
            <p class="mb-0">&copy; 2024 منصة التعليم التفاعلي. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>