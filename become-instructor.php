<?php
require_once 'config/config.php';
require_once 'includes/header.php';

$success = $error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = sanitize($_POST['full_name']);
    $email = sanitize($_POST['email']);
    $phone = sanitize($_POST['phone']);
    $bio = sanitize($_POST['bio']);
    $experience = sanitize($_POST['experience']);
    $expertise = sanitize($_POST['expertise']);
    
    // Validate inputs
    if (empty($full_name) || empty($email) || empty($phone) || empty($bio) || empty($experience) || empty($expertise)) {
        $error = 'جميع الحقول مطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صالح';
    } else {
        // Check if email already exists
        $existing_user = fetchOne("SELECT id FROM users WHERE email = ?", [$email]);
        if ($existing_user) {
            $error = 'البريد الإلكتروني مسجل مسبقاً';
        } else {
            // Insert instructor application
            $sql = "INSERT INTO instructor_applications (
                full_name, email, phone, bio, experience, expertise, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())";
            
            if (query($sql, [$full_name, $email, $phone, $bio, $experience, $expertise])) {
                $success = 'تم استلام طلبك بنجاح. سنتواصل معك قريباً';
                
                // Send email notification
                $to = ADMIN_EMAIL;
                $headers = "From: $email\r\n";
                $headers .= "Reply-To: $email\r\n";
                $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
                
                $email_body = "
                    <h3>طلب جديد للانضمام كمدرس</h3>
                    <p><strong>الاسم:</strong> $full_name</p>
                    <p><strong>البريد الإلكتروني:</strong> $email</p>
                    <p><strong>الهاتف:</strong> $phone</p>
                    <p><strong>نبذة:</strong> $bio</p>
                    <p><strong>الخبرة:</strong> $experience</p>
                    <p><strong>التخصص:</strong> $expertise</p>
                ";
                
                mail($to, "طلب جديد للانضمام كمدرس", $email_body, $headers);
            } else {
                $error = 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى';
            }
        }
    }
}
?>

<!-- Hero Section -->
<section class="bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">كن مدرساً معنا</h1>
                <p class="lead mb-4">
                    شارك معرفتك مع آلاف الطلاب حول العالم واكسب دخلاً إضافياً من خلال تدريس دوراتك الخاصة
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Requirements Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-graduation-cap fa-3x text-primary mb-3"></i>
                        <h4 class="card-title">خبرة في المجال</h4>
                        <p class="card-text text-muted">
                            خبرة عملية لا تقل عن 3 سنوات في مجال تخصصك
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chalkboard-teacher fa-3x text-primary mb-3"></i>
                        <h4 class="card-title">مهارات تدريسية</h4>
                        <p class="card-text text-muted">
                            القدرة على شرح المفاهيم المعقدة بطريقة بسيطة وواضحة
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-laptop fa-3x text-primary mb-3"></i>
                        <h4 class="card-title">مهارات تقنية</h4>
                        <p class="card-text text-muted">
                            إجادة استخدام الحاسوب والإنترنت وتطبيقات التعليم الإلكتروني
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Application Form -->
<section class="bg-light py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title text-center mb-4">تقديم طلب الانضمام</h3>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="bio" class="form-label">نبذة عنك</label>
                                <textarea class="form-control" id="bio" name="bio" rows="3" required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="experience" class="form-label">الخبرة</label>
                                <textarea class="form-control" id="experience" name="experience" rows="3" required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="expertise" class="form-label">مجال التخصص</label>
                                <input type="text" class="form-control" id="expertise" name="expertise" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                تقديم الطلب
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?> 