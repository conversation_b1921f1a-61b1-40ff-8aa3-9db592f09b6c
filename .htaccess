# منصة التعليم التفاعلي - إعدادات Apache

# تفعيل إعادة الكتابة
RewriteEngine On

# إعادة توجيه الروابط المُرمزة
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^(.*)$ /$1? [R=301,L]

# إعادة توجيه الأخطاء الشائعة
RewriteCond %{REQUEST_URI} !-f
RewriteCond %{REQUEST_URI} !-d
RewriteRule ^instructor/dashboard\.php$ /instructor/dashboard.php [R=301,L]

# صفحة خطأ مخصصة
ErrorDocument 404 /redirect.php?error=404

# تحسين الأداء
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# حماية الملفات الحساسة
<Files "config/*.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database/*.sql">
    Order Allow,Deny
    Deny from all
</Files>

# السماح بالوصول للملفات المطلوبة
<Files "config/database_auto.php">
    Order Allow,Deny
    Allow from all
</Files>
