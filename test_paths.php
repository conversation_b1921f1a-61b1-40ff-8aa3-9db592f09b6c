<?php
// Test script to check paths and redirects
require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>اختبار المسارات وإعادة التوجيه</h2>";

// Test APP_URL
echo "<h3>إعدادات التطبيق:</h3>";
echo "<p>APP_URL: " . APP_URL . "</p>";
echo "<p>APP_NAME: " . APP_NAME . "</p>";

// Test current URL
echo "<h3>المسار الحالي:</h3>";
echo "<p>Current URL: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";

// Test file existence
echo "<h3>اختبار وجود الملفات:</h3>";
$files_to_check = [
    'login.php',
    'register.php',
    'dashboard.php',
    'student/dashboard.php',
    'student/header.php',
    'student/footer.php',
    'logout.php'
];

foreach ($files_to_check as $file) {
    $exists = file_exists($file);
    $status = $exists ? '✓' : '✗';
    $color = $exists ? 'green' : 'red';
    echo "<p style='color: $color;'>$status $file</p>";
}

// Test authentication status
echo "<h3>حالة المصادقة:</h3>";
echo "<p>isAuthenticated(): " . (isAuthenticated() ? 'true' : 'false') . "</p>";

if (isAuthenticated()) {
    echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
    echo "<p>User Role: " . $_SESSION['user_role'] . "</p>";
    echo "<p>User Name: " . $_SESSION['user_name'] . "</p>";
}

// Test redirect function
echo "<h3>اختبار دالة إعادة التوجيه:</h3>";
echo "<p>url('login.php'): " . url('login.php') . "</p>";
echo "<p>url('student/dashboard.php'): " . url('student/dashboard.php') . "</p>";

echo "<hr>";
echo "<h3>روابط الاختبار:</h3>";
echo "<p><a href='login.php'>تسجيل الدخول</a></p>";
echo "<p><a href='register.php'>التسجيل</a></p>";
echo "<p><a href='dashboard.php'>لوحة التحكم الرئيسية</a></p>";
echo "<p><a href='student/dashboard.php'>لوحة الطالب</a></p>";
echo "<p><a href='logout.php'>تسجيل الخروج</a></p>";
?>
