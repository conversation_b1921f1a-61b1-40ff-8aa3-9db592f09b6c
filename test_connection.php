<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال بقاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .test-content {
            padding: 2rem;
        }
        .status-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .status-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-database me-3"></i>اختبار الاتصال بقاعدة البيانات</h1>
            <p class="mb-0">تشخيص مشاكل الاتصال وإصلاحها</p>
        </div>
        
        <div class="test-content">
            <?php
            echo "<h4><i class='fas fa-cog me-2'></i>الإعدادات الحالية:</h4>";
            echo "<div class='code-block'>";
            echo "المضيف: localhost<br>";
            echo "المستخدم: root<br>";
            echo "كلمة المرور: root<br>";
            echo "قاعدة البيانات: elearning_platform<br>";
            echo "</div>";
            
            echo "<h4><i class='fas fa-search me-2'></i>اختبار الاتصال:</h4>";
            
            // Test 1: Basic MySQL connection
            echo "<h5>1. اختبار الاتصال الأساسي بـ MySQL:</h5>";
            try {
                $pdo_basic = new PDO("mysql:host=localhost", "root", "root");
                echo "<div class='status-success'><i class='fas fa-check-circle me-2'></i>نجح الاتصال بـ MySQL</div>";
                
                // Get available databases
                $stmt = $pdo_basic->query("SHOW DATABASES");
                $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo "<h5>2. قواعد البيانات المتاحة:</h5>";
                echo "<div class='code-block'>";
                foreach ($databases as $db) {
                    echo "- " . $db . "<br>";
                }
                echo "</div>";
                
                // Check if our database exists
                if (in_array('elearning_platform', $databases)) {
                    echo "<div class='status-success'><i class='fas fa-check-circle me-2'></i>قاعدة البيانات 'elearning_platform' موجودة</div>";
                    
                    // Test connection to our database
                    try {
                        $pdo_our = new PDO("mysql:host=localhost;dbname=elearning_platform;charset=utf8mb4", "root", "root");
                        echo "<div class='status-success'><i class='fas fa-check-circle me-2'></i>نجح الاتصال بقاعدة البيانات 'elearning_platform'</div>";
                        
                        // Check tables
                        $stmt = $pdo_our->query("SHOW TABLES");
                        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        echo "<h5>3. الجداول الموجودة:</h5>";
                        echo "<div class='code-block'>";
                        if (empty($tables)) {
                            echo "لا توجد جداول في قاعدة البيانات";
                        } else {
                            foreach ($tables as $table) {
                                echo "- " . $table . "<br>";
                            }
                        }
                        echo "</div>";
                        
                        if (empty($tables)) {
                            echo "<div class='status-warning'>";
                            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                            echo "<strong>المشكلة:</strong> قاعدة البيانات فارغة - تحتاج لإنشاء الجداول<br>";
                            echo "<strong>الحل:</strong> تشغيل ملف إعداد قاعدة البيانات";
                            echo "</div>";
                            
                            echo "<div class='mt-3'>";
                            echo "<a href='database_setup.html' class='btn btn-primary me-2'>";
                            echo "<i class='fas fa-tools me-2'></i>إعداد قاعدة البيانات";
                            echo "</a>";
                            echo "<a href='setup_database.php' class='btn btn-secondary'>";
                            echo "<i class='fas fa-play me-2'></i>تشغيل الإعداد مباشرة";
                            echo "</a>";
                            echo "</div>";
                        } else {
                            echo "<div class='status-success'>";
                            echo "<i class='fas fa-check-circle me-2'></i>";
                            echo "<strong>ممتاز!</strong> قاعدة البيانات جاهزة للاستخدام";
                            echo "</div>";
                            
                            echo "<div class='mt-3'>";
                            echo "<a href='index.php' class='btn btn-success me-2'>";
                            echo "<i class='fas fa-home me-2'></i>عرض الصفحة الرئيسية";
                            echo "</a>";
                            echo "<a href='courses.php' class='btn btn-info'>";
                            echo "<i class='fas fa-graduation-cap me-2'></i>عرض الدورات";
                            echo "</a>";
                            echo "</div>";
                        }
                        
                    } catch (PDOException $e) {
                        echo "<div class='status-error'>";
                        echo "<i class='fas fa-times-circle me-2'></i>";
                        echo "<strong>خطأ في الاتصال بقاعدة البيانات:</strong><br>";
                        echo $e->getMessage();
                        echo "</div>";
                    }
                    
                } else {
                    echo "<div class='status-error'>";
                    echo "<i class='fas fa-times-circle me-2'></i>";
                    echo "<strong>المشكلة:</strong> قاعدة البيانات 'elearning_platform' غير موجودة<br>";
                    echo "<strong>الحل:</strong> إنشاء قاعدة البيانات أولاً";
                    echo "</div>";
                    
                    // Try to create the database
                    try {
                        $pdo_basic->exec("CREATE DATABASE IF NOT EXISTS elearning_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                        echo "<div class='status-success'>";
                        echo "<i class='fas fa-check-circle me-2'></i>";
                        echo "تم إنشاء قاعدة البيانات 'elearning_platform' بنجاح!";
                        echo "</div>";
                        
                        echo "<div class='mt-3'>";
                        echo "<a href='database_setup.html' class='btn btn-primary me-2'>";
                        echo "<i class='fas fa-tools me-2'></i>إعداد الجداول والبيانات";
                        echo "</a>";
                        echo "<a href='test_connection.php' class='btn btn-secondary'>";
                        echo "<i class='fas fa-refresh me-2'></i>إعادة الاختبار";
                        echo "</a>";
                        echo "</div>";
                        
                    } catch (PDOException $e) {
                        echo "<div class='status-error'>";
                        echo "<i class='fas fa-times-circle me-2'></i>";
                        echo "<strong>فشل في إنشاء قاعدة البيانات:</strong><br>";
                        echo $e->getMessage();
                        echo "</div>";
                    }
                }
                
            } catch (PDOException $e) {
                echo "<div class='status-error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>فشل الاتصال بـ MySQL:</strong><br>";
                echo $e->getMessage() . "<br><br>";
                echo "<strong>الأسباب المحتملة:</strong><br>";
                echo "- خادم MySQL غير مُشغل<br>";
                echo "- كلمة مرور خاطئة<br>";
                echo "- إعدادات MAMP غير صحيحة<br>";
                echo "</div>";
                
                echo "<div class='status-warning'>";
                echo "<i class='fas fa-lightbulb me-2'></i>";
                echo "<strong>خطوات الحل:</strong><br>";
                echo "1. تأكد من تشغيل MAMP<br>";
                echo "2. تحقق من إعدادات MySQL في MAMP<br>";
                echo "3. جرب كلمة مرور فارغة بدلاً من 'root'<br>";
                echo "</div>";
            }
            ?>
            
            <div class="mt-4">
                <h4><i class="fas fa-tools me-2"></i>أدوات مفيدة:</h4>
                <div class="row">
                    <div class="col-md-6">
                        <a href="database_setup.html" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-database me-2"></i>إعداد قاعدة البيانات
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="test_connection.php" class="btn btn-outline-secondary w-100 mb-2">
                            <i class="fas fa-refresh me-2"></i>إعادة الاختبار
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
