# منصة التعليم التفاعلي (Interactive Learning Platform)

منصة تعليمية متكاملة لبيع وإدارة الدورات التعليمية عبر الإنترنت.

## المميزات الرئيسية

- 🎓 نظام إدارة تعلم متكامل (LMS)
- 📚 إدارة الدورات والمحتوى التعليمي
- 👨‍🏫 لوحة تحكم للمدرسين
- 👨‍🎓 لوحة تحكم للطلاب
- 💳 نظام دفع متكامل
- 📜 إصدار شهادات تلقائي
- 📱 تصميم متجاوب
- 🌐 دعم اللغة العربية والإنجليزية

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- Composer
- Node.js & NPM (للتطوير)

## التثبيت

1. قم بنسخ المشروع:
```bash
git clone https://github.com/yourusername/elearning-platform.git
cd elearning-platform
```

2. قم بتثبيت اعتماديات PHP:
```bash
composer install
```

3. قم بتثبيت اعتماديات JavaScript:
```bash
npm install
```

4. قم بإنشاء ملف `.env` من ملف `.env.example`:
```bash
cp .env.example .env
```

5. قم بتعديل إعدادات قاعدة البيانات في ملف `.env`:
```
DB_HOST=localhost
DB_NAME=elearning_platform
DB_USER=your_username
DB_PASS=your_password
```

6. قم بإنشاء قاعدة البيانات وجداولها:
```bash
php database/schema.sql
```

7. قم بإنشاء مجلدات التخزين:
```bash
mkdir -p uploads/courses uploads/profiles uploads/materials
chmod -R 777 uploads
```

8. قم بتشغيل الخادم المحلي:
```bash
php -S localhost:8000
```

## هيكل المشروع

```
elearning-platform/
├── assets/              # ملفات CSS و JavaScript
├── config/             # ملفات الإعدادات
├── database/           # ملفات قاعدة البيانات
├── includes/           # ملفات PHP المشتركة
├── uploads/            # ملفات التحميل
│   ├── courses/       # صور الدورات
│   ├── profiles/      # صور الملفات الشخصية
│   └── materials/     # مواد الدروس
├── vendor/            # اعتماديات Composer
├── .env               # متغيرات البيئة
├── composer.json      # اعتماديات PHP
└── package.json       # اعتماديات JavaScript
```

## الأمان

- تأكد من تعيين الصلاحيات المناسبة لمجلدات التحميل
- قم بتغيير كلمات المرور الافتراضية
- قم بتفعيل HTTPS على الخادم
- قم بتحديث المتغيرات الحساسة في ملف `.env`

## المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بعمل Commit للتغييرات (`git commit -m 'Add some amazing feature'`)
4. قم بعمل Push للفرع (`git push origin feature/amazing-feature`)
5. قم بفتح طلب Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهتك أي مشكلة أو لديك أي استفسار، يرجى فتح issue جديد في GitHub.

## الشكر

شكر خاص لجميع المساهمين في هذا المشروع. 