<?php
session_start();
require_once '../config/config.php';
require_once '../config/database_auto.php';

// Check if user is logged in and is an instructor
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'instructor') {
    header('Location: ../login.php');
    exit;
}

$instructor_id = $_SESSION['user_id'];

// Get instructor's courses with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

try {
    // Get total courses count
    $total_courses = fetch("SELECT COUNT(*) as count FROM courses WHERE instructor_id = ?", [$instructor_id])['count'];
    $total_pages = ceil($total_courses / $per_page);
    
    // Get courses for current page
    $courses = fetchAll("
        SELECT 
            c.*,
            COUNT(DISTINCT e.id) as enrolled_students,
            COUNT(DISTINCT l.id) as total_lessons,
            COALESCE(AVG(r.rating), 0) as average_rating,
            cat.name as category_name
        FROM courses c
        LEFT JOIN enrollments e ON c.id = e.course_id
        LEFT JOIN lessons l ON c.id = l.course_id
        LEFT JOIN reviews r ON c.id = r.course_id
        LEFT JOIN categories cat ON c.category_id = cat.id
        WHERE c.instructor_id = ?
        GROUP BY c.id
        ORDER BY c.created_at DESC
        LIMIT ? OFFSET ?
    ", [$instructor_id, $per_page, $offset]);
} catch (Exception $e) {
    $courses = [];
    $total_courses = 0;
    $total_pages = 0;
}

$page_title = 'دوراتي';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
            --shadow-medium: 0 10px 30px rgba(0,0,0,0.1);
            --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
            --border-radius: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: var(--shadow-medium);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: white !important;
        }

        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            margin: 0 5px;
            padding: 8px 16px !important;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white !important;
            transform: translateY(-1px);
        }

        .hero-section {
            background: var(--primary-gradient);
            color: white;
            padding: 3rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .course-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
            border: none;
            height: 100%;
        }

        .course-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-heavy);
        }

        .course-thumbnail {
            height: 200px;
            object-fit: cover;
            transition: var(--transition);
            width: 100%;
        }

        .course-card:hover .course-thumbnail {
            transform: scale(1.1);
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: var(--transition);
            text-decoration: none;
            display: inline-block;
        }

        .btn-gradient:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .status-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-published { background: var(--success-gradient); color: white; }
        .status-draft { background: var(--warning-gradient); color: white; }
        .status-archived { background: var(--secondary-gradient); color: white; }

        .rating-stars {
            color: #ffc107;
            font-size: 0.9rem;
        }

        .breadcrumb {
            background: transparent;
            padding: 1rem 0;
        }

        .breadcrumb-item a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: white;
        }

        .stats-summary {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: var(--shadow-light);
            margin-bottom: 2rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #718096;
            font-size: 0.9rem;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
        }

        .empty-state i {
            font-size: 4rem;
            color: #cbd5e0;
            margin-bottom: 2rem;
        }

        /* Footer Styles */
        footer {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%) !important;
        }

        footer h5, footer h6 {
            color: #e2e8f0 !important;
            font-weight: 600;
        }

        footer .text-light {
            color: #cbd5e0 !important;
            transition: var(--transition);
        }

        footer .text-light:hover {
            color: #667eea !important;
            text-decoration: none;
        }

        footer .fab, footer .fas {
            transition: var(--transition);
        }

        footer .fab:hover, footer .fas:hover {
            transform: translateY(-2px);
            color: #667eea !important;
        }

        footer .input-group .form-control {
            border-radius: 25px 0 0 25px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        footer .input-group .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        footer .input-group .btn {
            border-radius: 0 25px 25px 0;
            background: var(--primary-gradient);
            border: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-section {
                padding: 2rem 0 1rem;
            }

            .stats-summary {
                padding: 1.5rem;
            }

            .stat-item {
                padding: 0.5rem;
                margin-bottom: 1rem;
            }

            .course-card .d-flex.gap-2 {
                flex-direction: column;
                gap: 0.5rem !important;
            }

            footer .col-lg-4, footer .col-lg-2 {
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="../index.php">
            <i class="fas fa-graduation-cap me-2"></i>
            منصة التعليم التفاعلي
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">لوحة التحكم</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="courses.php">دوراتي</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="add-course.php">إضافة دورة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="students.php">الطلاب</a>
                </li>
            </ul>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn btn-link text-white text-decoration-none dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2"></i>
                        <?php echo htmlspecialchars($_SESSION['user_name']); ?>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i> الملف الشخصي
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="dashboard.php">لوحة التحكم</a></li>
                <li class="breadcrumb-item active">دوراتي</li>
            </ol>
        </nav>
        
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-3">دوراتي</h1>
                <p class="lead mb-0">إدارة ومتابعة جميع دوراتك التعليمية</p>
            </div>
            <div class="col-md-4 text-center">
                <a href="add-course.php" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>إضافة دورة جديدة
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Courses Section -->
<section class="py-5">
    <div class="container">
        <!-- Statistics Summary -->
        <div class="stats-summary">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $total_courses; ?></div>
                        <div class="stat-label">إجمالي الدورات</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">
                            <?php
                            $published = 0;
                            foreach ($courses as $course) {
                                if ($course['status'] === 'published') $published++;
                            }
                            echo $published;
                            ?>
                        </div>
                        <div class="stat-label">دورات منشورة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">
                            <?php
                            $total_students = 0;
                            foreach ($courses as $course) {
                                $total_students += $course['enrolled_students'];
                            }
                            echo $total_students;
                            ?>
                        </div>
                        <div class="stat-label">إجمالي الطلاب</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">
                            <?php
                            $total_lessons = 0;
                            foreach ($courses as $course) {
                                $total_lessons += $course['total_lessons'];
                            }
                            echo $total_lessons;
                            ?>
                        </div>
                        <div class="stat-label">إجمالي الدروس</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Courses Grid -->
        <?php if (empty($courses)): ?>
            <div class="empty-state">
                <i class="fas fa-book-open"></i>
                <h3 class="mb-3">لا توجد دورات بعد</h3>
                <p class="text-muted mb-4">ابدأ رحلتك التعليمية بإنشاء دورتك الأولى</p>
                <a href="add-course.php" class="btn btn-gradient btn-lg">
                    <i class="fas fa-plus me-2"></i>إنشاء دورة جديدة
                </a>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($courses as $course): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="course-card">
                            <div class="position-relative overflow-hidden">
                                <img src="<?php echo $course['thumbnail'] ? '../' . $course['thumbnail'] : '../assets/images/default-course.jpg'; ?>"
                                     class="course-thumbnail"
                                     alt="<?php echo htmlspecialchars($course['title']); ?>"
                                     onerror="this.src='../assets/images/default-course.jpg'">

                                <!-- Status Badge -->
                                <div class="status-badge status-<?php echo $course['status']; ?>">
                                    <?php
                                    switch($course['status']) {
                                        case 'published': echo 'منشور'; break;
                                        case 'draft': echo 'مسودة'; break;
                                        case 'archived': echo 'مؤرشف'; break;
                                        default: echo $course['status'];
                                    }
                                    ?>
                                </div>
                            </div>

                            <div class="p-4">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <span class="badge bg-primary"><?php echo htmlspecialchars($course['category_name'] ?? 'غير محدد'); ?></span>
                                    <span class="text-muted small"><?php echo date('Y/m/d', strtotime($course['created_at'])); ?></span>
                                </div>

                                <h5 class="fw-bold mb-3"><?php echo htmlspecialchars($course['title']); ?></h5>
                                <p class="text-muted mb-3">
                                    <?php echo htmlspecialchars(substr($course['description'], 0, 100)) . '...'; ?>
                                </p>

                                <!-- Course Stats -->
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="d-flex align-items-center">
                                        <?php if ($course['average_rating'] > 0): ?>
                                            <div class="rating-stars me-2">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star<?php echo $i <= $course['average_rating'] ? '' : '-o'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                            <span class="text-muted small">(<?php echo number_format($course['average_rating'], 1); ?>)</span>
                                        <?php else: ?>
                                            <span class="text-muted small">لا توجد تقييمات</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-muted small">
                                        <i class="fas fa-users me-1"></i>
                                        <?php echo $course['enrolled_students']; ?> طالب
                                    </div>
                                </div>

                                <!-- Course Info -->
                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <small class="text-muted d-block">الدروس</small>
                                        <strong><?php echo $course['total_lessons']; ?></strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted d-block">المدة</small>
                                        <strong><?php echo $course['duration'] ? floor($course['duration'] / 60) . 'ساعة' : 'غير محدد'; ?></strong>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted d-block">السعر</small>
                                        <strong><?php echo $course['price'] > 0 ? number_format($course['price'], 0) . ' ريال' : 'مجاني'; ?></strong>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="d-flex gap-2">
                                    <a href="edit-course.php?id=<?php echo $course['id']; ?>" class="btn btn-outline-primary btn-sm flex-fill">
                                        <i class="fas fa-edit me-1"></i>تعديل
                                    </a>
                                    <a href="../course.php?id=<?php echo $course['id']; ?>" class="btn btn-outline-info btn-sm flex-fill" target="_blank">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </a>
                                    <a href="course-lessons.php?id=<?php echo $course['id']; ?>" class="btn btn-outline-success btn-sm flex-fill">
                                        <i class="fas fa-play me-1"></i>الدروس
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="صفحات الدورات" class="mt-5">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>">السابق</a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>">التالي</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</section>

<!-- Footer -->
<footer class="bg-dark text-white mt-5">
    <div class="container py-5">
        <div class="row">
            <!-- About Section -->
            <div class="col-lg-4 col-md-6 mb-4">
                <h5 class="mb-3">
                    <i class="fas fa-graduation-cap me-2"></i>
                    منصة التعليم التفاعلي
                </h5>
                <p class="text-light mb-3">
                    منصة تعليمية متطورة تهدف إلى تقديم أفضل تجربة تعليمية تفاعلية للطلاب في الوطن العربي
                    من خلال دورات عالية الجودة ومحتوى تعليمي متميز.
                </p>
                <div class="d-flex">
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-facebook"></i></a>
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-instagram"></i></a>
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="text-light fs-5"><i class="fab fa-youtube"></i></a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="mb-3">روابط سريعة</h6>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="dashboard.php" class="text-light text-decoration-none">لوحة التحكم</a></li>
                    <li class="mb-2"><a href="courses.php" class="text-light text-decoration-none">دوراتي</a></li>
                    <li class="mb-2"><a href="add-course.php" class="text-light text-decoration-none">إضافة دورة</a></li>
                    <li class="mb-2"><a href="students.php" class="text-light text-decoration-none">الطلاب</a></li>
                </ul>
            </div>

            <!-- Support -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="mb-3">الدعم</h6>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="#" class="text-light text-decoration-none">مركز المساعدة</a></li>
                    <li class="mb-2"><a href="#" class="text-light text-decoration-none">الأسئلة الشائعة</a></li>
                    <li class="mb-2"><a href="../contact.php" class="text-light text-decoration-none">اتصل بنا</a></li>
                    <li class="mb-2"><a href="#" class="text-light text-decoration-none">بلاغ مشكلة</a></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div class="col-lg-4 col-md-6 mb-4">
                <h6 class="mb-3">معلومات التواصل</h6>
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-envelope me-2"></i>
                    <span><EMAIL></span>
                </div>
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-phone me-2"></i>
                    <span>+966 11 123 4567</span>
                </div>
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <span>الرياض، المملكة العربية السعودية</span>
                </div>

                <!-- Newsletter -->
                <div class="mt-3">
                    <h6 class="mb-2">اشترك في النشرة الإخبارية</h6>
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="بريدك الإلكتروني">
                        <button class="btn btn-primary" type="button">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <hr class="my-4">

        <!-- Bottom Footer -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0">&copy; 2024 منصة التعليم التفاعلي. جميع الحقوق محفوظة.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="#" class="text-light text-decoration-none me-3">سياسة الخصوصية</a>
                <a href="#" class="text-light text-decoration-none me-3">شروط الاستخدام</a>
                <a href="#" class="text-light text-decoration-none">ملفات تعريف الارتباط</a>
            </div>
        </div>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Add loading animation for course cards
document.addEventListener('DOMContentLoaded', function() {
    const courseCards = document.querySelectorAll('.course-card');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });

    courseCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        card.style.transitionDelay = `${index * 0.1}s`;
        observer.observe(card);
    });
});

// Add smooth scrolling
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth'
            });
        }
    });
});

// Add confirmation for delete actions (if implemented)
document.querySelectorAll('.btn-danger').forEach(btn => {
    btn.addEventListener('click', function(e) {
        if (!confirm('هل أنت متأكد من هذا الإجراء؟')) {
            e.preventDefault();
        }
    });
});

// Add tooltips to action buttons
document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(element => {
    new bootstrap.Tooltip(element);
});
</script>

</body>
</html>
