<?php
require_once 'config/config.php';
require_once 'includes/header.php';

$success = $error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $subject = sanitize($_POST['subject']);
    $message = sanitize($_POST['message']);
    
    // Validate inputs
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error = 'جميع الحقول مطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صالح';
    } else {
        // Insert message into database
        $sql = "INSERT INTO messages (name, email, subject, message, created_at) VALUES (?, ?, ?, ?, NOW())";
        if (query($sql, [$name, $email, $subject, $message])) {
            $success = 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً';
            
            // Send email notification
            $to = ADMIN_EMAIL;
            $headers = "From: $email\r\n";
            $headers .= "Reply-To: $email\r\n";
            $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
            
            $email_body = "
                <h3>رسالة جديدة من موقع " . APP_NAME . "</h3>
                <p><strong>الاسم:</strong> $name</p>
                <p><strong>البريد الإلكتروني:</strong> $email</p>
                <p><strong>الموضوع:</strong> $subject</p>
                <p><strong>الرسالة:</strong></p>
                <p>$message</p>
            ";
            
            mail($to, "رسالة جديدة: $subject", $email_body, $headers);
        } else {
            $error = 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى';
        }
    }
}
?>

<!-- Hero Section -->
<section class="bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">اتصل بنا</h1>
                <p class="lead mb-4">
                    نحن هنا للإجابة على استفساراتك ومساعدتك في أي وقت
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title mb-4">أرسل لنا رسالة</h3>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="name" class="form-label">الاسم</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="subject" class="form-label">الموضوع</label>
                                <input type="text" class="form-control" id="subject" name="subject" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">الرسالة</label>
                                <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                إرسال الرسالة
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Contact Info -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-body">
                        <h3 class="card-title mb-4">معلومات الاتصال</h3>
                        
                        <ul class="list-unstyled">
                            <li class="mb-3">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                العنوان: القاهرة، مصر
                            </li>
                            <li class="mb-3">
                                <i class="fas fa-phone text-primary me-2"></i>
                                الهاتف: +20 123 456 789
                            </li>
                            <li class="mb-3">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                البريد الإلكتروني: <EMAIL>
                            </li>
                        </ul>
                        
                        <hr>
                        
                        <h4 class="mb-3">تابعنا</h4>
                        <div class="social-links">
                            <a href="#" class="btn btn-outline-primary me-2">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-outline-primary me-2">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="btn btn-outline-primary me-2">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="btn btn-outline-primary">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title mb-4">ساعات العمل</h3>
                        
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>الأحد - الخميس:</strong> 9:00 صباحاً - 5:00 مساءً
                            </li>
                            <li class="mb-2">
                                <strong>الجمعة:</strong> 10:00 صباحاً - 2:00 مساءً
                            </li>
                            <li>
                                <strong>السبت:</strong> مغلق
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?> 