<?php
require_once 'config/config.php';
require_once 'config/database_auto.php';

// Check if user is authenticated
if (!isAuthenticated()) {
    redirect('login.php');
}

// Redirect based on user role
switch ($_SESSION['user_role']) {
    case 'admin':
        redirect('admin/dashboard.php');
        break;
    case 'instructor':
        redirect('instructor/dashboard.php');
        break;
    case 'student':
    default:
        redirect('student/dashboard.php');
        break;
}
?>
