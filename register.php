<?php
require_once 'config/config.php';
require_once 'config/database.php';

// Session is already started in config.php

// Redirect if already logged in
if (isAuthenticated()) {
    redirect('dashboard.php');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim(strip_tags($_POST['full_name']));
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $role = 'student'; // Default role
    
    // Validation
    if (empty($full_name) || empty($email) || empty($password) || empty($confirm_password)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'يرجى إدخال بريد إلكتروني صحيح';
    } elseif (strlen($full_name) < 2) {
        $error = 'يجب أن يكون الاسم الكامل حرفين على الأقل';
    } elseif (strlen($password) < 8) {
        $error = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمات المرور غير متطابقة';
    } elseif (!isset($_POST['terms'])) {
        $error = 'يجب الموافقة على الشروط والأحكام';
    } else {
        // Check if email already exists
        $sql = "SELECT id FROM users WHERE email = ?";
        $existing_user = fetch($sql, [$email]);
        
        if ($existing_user) {
            $error = 'البريد الإلكتروني مسجل مسبقاً';
        } else {
            // Create user
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // Generate username from email (part before @)
            $username = strtolower(explode('@', $email)[0]);

            // Check if username already exists and make it unique if needed
            $original_username = $username;
            $counter = 1;
            while (fetch("SELECT id FROM users WHERE username = ?", [$username])) {
                $username = $original_username . $counter;
                $counter++;
            }

            $user_data = [
                'username' => $username,
                'full_name' => $full_name,
                'email' => $email,
                'password' => $hashed_password,
                'role' => $role
            ];
            
            $user_id = insert('users', $user_data);
            if ($user_id) {
                $success = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
                // Clear form data on success
                $full_name = $email = '';
            } else {
                global $conn;
                $error = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى';
                // إظهار رسالة الخطأ من قاعدة البيانات أثناء التطوير فقط
                if (isset($conn) && $conn->errorInfo()[2]) {
                    $error .= '<br><small style="color: #666;">';
                    $error .= 'تفاصيل الخطأ: ' . htmlspecialchars($conn->errorInfo()[2]);
                    $error .= '</small>';
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - <?php echo APP_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow-lg">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h1 class="h3">إنشاء حساب جديد</h1>
                            <p class="text-muted">انضم إلينا وابدأ رحلة التعلم</p>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="full_name" name="full_name"
                                           value="<?php echo htmlspecialchars($full_name ?? ''); ?>" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required minlength="8">
                                </div>
                                <div class="form-text">يجب أن تكون 8 أحرف على الأقل</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    أوافق على <a href="terms.php" class="text-decoration-none">الشروط والأحكام</a>
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100 mb-3">إنشاء حساب</button>
                            
                            <div class="text-center">
                                <p class="mb-0">لديك حساب بالفعل؟ <a href="login.php" class="text-decoration-none">تسجيل الدخول</a></p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <script>
    // Form validation
    (function () {
        'use strict'
        var forms = document.querySelectorAll('.needs-validation')
        Array.prototype.slice.call(forms).forEach(function (form) {
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault()
                    event.stopPropagation()
                }
                form.classList.add('was-validated')
            }, false)
        })
    })()
    
    // Password confirmation validation
    document.getElementById('confirm_password').addEventListener('input', function() {
        if (this.value !== document.getElementById('password').value) {
            this.setCustomValidity('كلمات المرور غير متطابقة');
        } else {
            this.setCustomValidity('');
        }
    });
    </script>
</body>
</html> 