<?php
require_once '../config/config.php';
require_once '../includes/header.php';

// Check if user is logged in and is an instructor
if (!isAuthenticated() || $_SESSION['user_role'] !== 'instructor') {
    redirect('../login.php');
}

// Get instructor's courses
$courses = fetchAll("
    SELECT 
        c.*,
        COUNT(DISTINCT e.id) as enrolled_students,
        COUNT(DISTINCT l.id) as total_lessons
    FROM courses c
    LEFT JOIN enrollments e ON c.id = e.course_id
    LEFT JOIN lessons l ON c.id = l.course_id
    WHERE c.instructor_id = ?
    GROUP BY c.id
    ORDER BY c.created_at DESC
", [$_SESSION['user_id']]);

// Get total statistics
$stats = fetch("
    SELECT
        COUNT(DISTINCT c.id) as total_courses,
        COUNT(DISTINCT e.id) as total_students,
        COUNT(DISTINCT l.id) as total_lessons
    FROM courses c
    LEFT JOIN enrollments e ON c.id = e.course_id
    LEFT JOIN lessons l ON c.id = l.course_id
    WHERE c.instructor_id = ?
", [$_SESSION['user_id']]);
?>

<!-- Dashboard Header -->
<section class="bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">مرحباً، <?php echo $_SESSION['user_name']; ?></h1>
                <p class="lead mb-4">
                    إدارة دوراتك التعليمية ومتابعة تقدم طلابك
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="add-course.php" class="btn btn-light">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء دورة جديدة
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Statistics Cards -->
            <div class="col-12 mb-5">
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card bg-primary text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-uppercase mb-2">الدورات</h6>
                                        <h2 class="mb-0"><?php echo $stats['total_courses']; ?></h2>
                                    </div>
                                    <i class="fas fa-book fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card bg-success text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-uppercase mb-2">الطلاب</h6>
                                        <h2 class="mb-0"><?php echo $stats['total_students']; ?></h2>
                                    </div>
                                    <i class="fas fa-users fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card bg-info text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-uppercase mb-2">الدروس</h6>
                                        <h2 class="mb-0"><?php echo $stats['total_lessons']; ?></h2>
                                    </div>
                                    <i class="fas fa-play-circle fa-2x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Courses List -->
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0">دوراتي</h3>
                            <a href="courses.php" class="btn btn-primary btn-sm">عرض الكل</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($courses)): ?>
                            <div class="alert alert-info">
                                لم تقم بإنشاء أي دورات بعد.
                                <a href="add-course.php" class="alert-link">ابدأ بإنشاء دورة جديدة</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الدورة</th>
                                            <th>الطلاب</th>
                                            <th>الدروس</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($courses as $course): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo $course['thumbnail'] ?? '../assets/images/default-course.jpg'; ?>" 
                                                             alt="<?php echo $course['title']; ?>" 
                                                             class="rounded me-3"
                                                             style="width: 50px; height: 50px; object-fit: cover;">
                                                        <div>
                                                            <h6 class="mb-0"><?php echo $course['title']; ?></h6>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo $course['enrolled_students']; ?></td>
                                                <td><?php echo $course['total_lessons']; ?></td>
                                                <td>
                                                    <?php if ($course['status'] === 'published'): ?>
                                                        <span class="badge bg-success">منشور</span>
                                                    <?php elseif ($course['status'] === 'draft'): ?>
                                                        <span class="badge bg-warning">مسودة</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">محظور</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="edit-course.php?id=<?php echo $course['id']; ?>" 
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="../course.php?id=<?php echo $course['id']; ?>" 
                                                           class="btn btn-sm btn-outline-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once '../includes/footer.php'; ?> 