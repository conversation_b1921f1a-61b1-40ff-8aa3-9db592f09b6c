<?php
session_start();
require_once '../config/config.php';
require_once '../config/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Sample enrolled courses data (since database might not be set up)
$sample_courses = [
    [
        'id' => 1,
        'title' => 'تطوير المواقع باستخدام HTML و CSS',
        'description' => 'تعلم أساسيات تطوير المواقع من الصفر',
        'thumbnail' => 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=250&fit=crop',
        'instructor_name' => 'أحمد محمد',
        'duration' => 20,
        'total_lessons' => 15,
        'completed_lessons' => 8,
        'progress' => 53,
        'level' => 'مبتدئ'
    ],
    [
        'id' => 2,
        'title' => 'البرمجة بلغة JavaScript',
        'description' => 'دورة شاملة لتعلم JavaScript من المبتدئ إلى المحترف',
        'thumbnail' => 'https://images.unsplash.com/photo-1579468118864-1b9ea3c0db4a?w=400&h=250&fit=crop',
        'instructor_name' => 'فاطمة علي',
        'duration' => 35,
        'total_lessons' => 25,
        'completed_lessons' => 12,
        'progress' => 48,
        'level' => 'متوسط'
    ],
    [
        'id' => 3,
        'title' => 'تصميم واجهات المستخدم UI/UX',
        'description' => 'تعلم أساسيات تصميم واجهات المستخدم الحديثة',
        'thumbnail' => 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=250&fit=crop',
        'instructor_name' => 'سارة أحمد',
        'duration' => 28,
        'total_lessons' => 20,
        'completed_lessons' => 5,
        'progress' => 25,
        'level' => 'متوسط'
    ]
];

// Try to get courses from database, fallback to sample data
try {
    $enrolled_courses = fetch("
        SELECT
            c.*,
            u.full_name as instructor_name
        FROM enrollments e
        JOIN courses c ON e.course_id = c.id
        JOIN users u ON c.instructor_id = u.id
        WHERE e.user_id = ?
        ORDER BY e.created_at DESC
    ", [$_SESSION['user_id']]);

    if (!$enrolled_courses) {
        $enrolled_courses = $sample_courses;
    }
} catch (Exception $e) {
    $enrolled_courses = $sample_courses;
}

// Sample activities data
$activities = [
    [
        'description' => 'أكملت درس "مقدمة في HTML" في دورة تطوير المواقع',
        'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
    ],
    [
        'description' => 'بدأت دورة جديدة: البرمجة بلغة JavaScript',
        'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
    ],
    [
        'description' => 'أكملت درس "أساسيات CSS" في دورة تطوير المواقع',
        'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
    ],
    [
        'description' => 'حصلت على شهادة إتمام دورة "أساسيات البرمجة"',
        'created_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
    ]
];

// Set user name
$user_name = $_SESSION['user_name'] ?? 'الطالب';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - منصة التعليم التفاعلي</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
            --shadow-medium: 0 10px 30px rgba(0,0,0,0.1);
            --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
            --border-radius: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: #2d3748;
        }

        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: var(--shadow-medium);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .nav-link {
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            margin: 0 5px;
            padding: 8px 16px !important;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        .welcome-card {
            background: var(--secondary-gradient);
            color: white;
            border-radius: var(--border-radius);
            padding: 3rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-heavy);
        }

        .welcome-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .welcome-content {
            position: relative;
            z-index: 2;
        }

        .stats-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
            border: none;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-heavy);
        }

        .stats-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stats-icon.primary { background: var(--primary-gradient); }
        .stats-icon.success { background: var(--success-gradient); }
        .stats-icon.warning { background: var(--warning-gradient); }
        .stats-icon.info { background: var(--info-gradient); }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .course-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
            border: none;
            height: 100%;
            position: relative;
        }

        .course-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-heavy);
        }

        .course-thumbnail {
            height: 220px;
            object-fit: cover;
            transition: var(--transition);
            width: 100%;
        }

        .course-card:hover .course-thumbnail {
            transform: scale(1.1);
        }

        .course-content {
            padding: 1.5rem;
        }

        .progress-ring {
            width: 100px;
            height: 100px;
            margin: 1rem auto;
        }

        .progress-ring circle {
            fill: transparent;
            stroke-width: 6;
            stroke-linecap: round;
        }

        .progress-ring .bg {
            stroke: #e2e8f0;
        }

        .progress-ring .progress {
            stroke: url(#gradient);
            stroke-dasharray: 251;
            stroke-dashoffset: 251;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
            animation: progress 2s ease-in-out forwards;
        }

        @keyframes progress {
            to {
                stroke-dashoffset: calc(251 - (251 * var(--progress)) / 100);
            }
        }

        .activity-item {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            border-left: 4px solid transparent;
            background-image: linear-gradient(white, white), var(--primary-gradient);
            background-origin: border-box;
            background-clip: content-box, border-box;
        }

        .activity-item:hover {
            transform: translateX(-8px);
            box-shadow: var(--shadow-medium);
        }

        .activity-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition);
        }

        .btn-gradient:hover::before {
            left: 100%;
        }

        .btn-gradient:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .level-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.95);
            color: #333;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-light);
        }

        .section-title {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 2rem;
            position: relative;
            padding-bottom: 1rem;
            font-size: 1.8rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #718096;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .course-stats {
            background: #f7fafc;
            border-radius: 12px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .course-stats .row > div {
            text-align: center;
            padding: 0.5rem;
        }

        .course-stats .fw-bold {
            color: #4a5568;
            font-size: 1.1rem;
        }

        .sidebar-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-medium);
            margin-bottom: 2rem;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .sidebar-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .quick-action-btn {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            transition: var(--transition);
            text-decoration: none;
            color: #4a5568;
            display: block;
            margin-bottom: 0.5rem;
        }

        .quick-action-btn:hover {
            border-color: #667eea;
            background: #f7fafc;
            color: #667eea;
            transform: translateX(5px);
        }

        @media (max-width: 768px) {
            .welcome-card {
                padding: 2rem;
                text-align: center;
            }

            .stats-card {
                margin-bottom: 1rem;
            }

            .course-card {
                margin-bottom: 1.5rem;
            }
        }
    </style>
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand fw-bold" href="../index.php">
            <i class="fas fa-graduation-cap me-2"></i>
            منصة التعليم التفاعلي
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        لوحة التحكم
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../courses.php">
                        <i class="fas fa-book me-1"></i>
                        الدورات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#certificates">
                        <i class="fas fa-certificate me-1"></i>
                        الشهادات
                    </a>
                </li>
            </ul>

            <div class="dropdown">
                <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-circle me-2"></i>
                    <?php echo htmlspecialchars($user_name); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                    <li><a class="dropdown-item" href="#settings"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<!-- Welcome Section -->
<div class="container mt-4">
    <div class="welcome-card">
        <div class="welcome-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">مرحباً بك، <?php echo htmlspecialchars($user_name); ?>! 👋</h1>
                    <p class="lead mb-4 opacity-90">استمر في رحلة التعلم واكتشف المزيد من الدورات المثيرة والمحتوى التعليمي المتميز</p>
                    <div class="d-flex flex-wrap gap-3">
                        <a href="../courses.php" class="btn btn-light btn-lg">
                            <i class="fas fa-search me-2"></i>
                            استكشف دورات جديدة
                        </a>
                        <a href="certificates-simple.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-certificate me-2"></i>
                            شهاداتي
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <div class="position-relative">
                        <i class="fas fa-graduation-cap" style="font-size: 6rem; opacity: 0.2;"></i>
                        <div class="position-absolute top-50 start-50 translate-middle">
                            <i class="fas fa-rocket text-white" style="font-size: 3rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="container mb-5">
    <div class="row g-4">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card text-center">
                <div class="stats-icon primary">
                    <i class="fas fa-book-open"></i>
                </div>
                <div class="stats-number"><?php echo count($enrolled_courses); ?></div>
                <p class="text-muted mb-0 fw-medium">الدورات المسجلة</p>
                <small class="text-muted">إجمالي الدورات المتاحة</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card text-center">
                <div class="stats-icon success">
                    <i class="fas fa-chart-line"></i>
                </div>
                <?php
                $total_progress = 0;
                foreach ($enrolled_courses as $course) {
                    $total_progress += $course['progress'];
                }
                $average_progress = count($enrolled_courses) > 0 ? $total_progress / count($enrolled_courses) : 0;
                ?>
                <div class="stats-number"><?php echo round($average_progress); ?>%</div>
                <p class="text-muted mb-0 fw-medium">متوسط التقدم</p>
                <small class="text-muted">نسبة الإنجاز الإجمالية</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card text-center">
                <div class="stats-icon warning">
                    <i class="fas fa-play-circle"></i>
                </div>
                <?php
                $total_lessons = 0;
                $completed_lessons = 0;
                foreach ($enrolled_courses as $course) {
                    $total_lessons += $course['total_lessons'];
                    $completed_lessons += $course['completed_lessons'];
                }
                ?>
                <div class="stats-number"><?php echo $completed_lessons; ?></div>
                <p class="text-muted mb-0 fw-medium">الدروس المكتملة</p>
                <small class="text-muted">من أصل <?php echo $total_lessons; ?> درس</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card text-center">
                <div class="stats-icon info">
                    <i class="fas fa-clock"></i>
                </div>
                <?php
                $total_hours = 0;
                foreach ($enrolled_courses as $course) {
                    $total_hours += $course['duration'];
                }
                ?>
                <div class="stats-number"><?php echo $total_hours; ?></div>
                <p class="text-muted mb-0 fw-medium">ساعات التعلم</p>
                <small class="text-muted">إجمالي المحتوى المتاح</small>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mb-5">
    <div class="row g-4">
        <!-- Enrolled Courses -->
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="section-title mb-0">دوراتي المسجلة</h2>
                <a href="../courses.php" class="btn btn-outline-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة دورة جديدة
                </a>
            </div>

            <?php if (empty($enrolled_courses)): ?>
                <div class="empty-state">
                    <i class="fas fa-book-open"></i>
                    <h4 class="mb-3">لم تقم بالتسجيل في أي دورة بعد</h4>
                    <p class="mb-4">ابدأ رحلة التعلم واستكشف الدورات المتاحة لتطوير مهاراتك</p>
                    <a href="../courses.php" class="btn-gradient">
                        <i class="fas fa-search me-2"></i>
                        استكشف الدورات
                    </a>
                </div>
            <?php else: ?>
                <div class="row g-4">
                    <?php foreach ($enrolled_courses as $course): ?>
                        <div class="col-md-6">
                            <div class="course-card position-relative">
                                <span class="level-badge"><?php echo htmlspecialchars($course['level']); ?></span>
                                <div class="position-relative overflow-hidden">
                                    <img src="<?php echo $course['thumbnail']; ?>"
                                         class="course-thumbnail"
                                         alt="<?php echo htmlspecialchars($course['title']); ?>">
                                </div>

                                <div class="course-content">
                                    <h5 class="fw-bold mb-2 text-truncate" title="<?php echo htmlspecialchars($course['title']); ?>">
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </h5>
                                    <p class="text-muted mb-3">
                                        <i class="fas fa-user me-2"></i>
                                        <?php echo htmlspecialchars($course['instructor_name']); ?>
                                    </p>

                                    <!-- Progress Circle with SVG Gradient -->
                                    <div class="text-center mb-3">
                                        <svg class="progress-ring" style="--progress: <?php echo $course['progress']; ?>">
                                            <defs>
                                                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                                                </linearGradient>
                                            </defs>
                                            <circle class="bg" cx="50" cy="50" r="40"></circle>
                                            <circle class="progress" cx="50" cy="50" r="40"></circle>
                                        </svg>
                                        <div class="position-absolute top-50 start-50 translate-middle">
                                            <div class="fw-bold text-primary fs-5"><?php echo $course['progress']; ?>%</div>
                                            <small class="text-muted">مكتمل</small>
                                        </div>
                                    </div>

                                    <!-- Course Stats -->
                                    <div class="course-stats">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <small class="text-muted d-block">الدروس</small>
                                                <div class="fw-bold"><?php echo $course['completed_lessons']; ?>/<?php echo $course['total_lessons']; ?></div>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">المدة</small>
                                                <div class="fw-bold"><?php echo $course['duration']; ?> ساعة</div>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">المستوى</small>
                                                <div class="fw-bold"><?php echo htmlspecialchars($course['level']); ?></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2 mt-3">
                                        <a href="../course.php?id=<?php echo $course['id']; ?>" class="btn-gradient">
                                            <i class="fas fa-play me-2"></i>
                                            متابعة التعلم
                                        </a>
                                        <div class="progress" style="height: 6px;">
                                            <div class="progress-bar bg-gradient"
                                                 style="width: <?php echo $course['progress']; ?>%; background: var(--primary-gradient);"
                                                 role="progressbar"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Recent Activities -->
            <div class="sidebar-card">
                <h5 class="fw-bold mb-4">
                    <i class="fas fa-history me-2 text-primary"></i>
                    النشاطات الأخيرة
                </h5>

                <?php if (empty($activities)): ?>
                    <div class="empty-state py-3">
                        <i class="fas fa-history"></i>
                        <p class="mb-0">لا توجد نشاطات حديثة</p>
                    </div>
                <?php else: ?>
                    <div class="activities-list">
                        <?php foreach ($activities as $index => $activity): ?>
                            <div class="activity-item">
                                <div class="d-flex align-items-start">
                                    <div class="activity-icon me-3">
                                        <?php
                                        $icons = ['fas fa-play', 'fas fa-book', 'fas fa-check-circle', 'fas fa-award'];
                                        echo '<i class="' . $icons[$index % 4] . '"></i>';
                                        ?>
                                    </div>
                                    <div class="flex-grow-1">
                                        <p class="mb-1 fw-medium"><?php echo htmlspecialchars($activity['description']); ?></p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo date('j M Y, H:i', strtotime($activity['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="text-center mt-3">
                        <a href="#all-activities" class="btn btn-sm btn-outline-primary">
                            عرض جميع النشاطات
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Learning Progress Chart -->
            <div class="sidebar-card">
                <h5 class="fw-bold mb-4">
                    <i class="fas fa-chart-pie me-2 text-primary"></i>
                    تقدم التعلم الإجمالي
                </h5>
                <div class="text-center position-relative">
                    <div style="width: 300px; height: 300px; margin: 0 auto;">
                        <canvas id="progressChart"></canvas>
                    </div>
                    <div class="mt-4">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="p-3 bg-light rounded">
                                    <div class="h4 fw-bold text-primary mb-1"><?php echo round($average_progress); ?>%</div>
                                    <small class="text-muted">مكتمل</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-light rounded">
                                    <div class="h4 fw-bold text-secondary mb-1"><?php echo 100 - round($average_progress); ?>%</div>
                                    <small class="text-muted">متبقي</small>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 p-3 bg-primary bg-opacity-10 rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-medium">إجمالي الدروس:</span>
                                <span class="badge bg-primary"><?php echo $completed_lessons; ?> / <?php echo $total_lessons; ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="sidebar-card">
                <h5 class="fw-bold mb-4">
                    <i class="fas fa-bolt me-2 text-primary"></i>
                    إجراءات سريعة
                </h5>
                <div class="d-grid gap-2">
                    <a href="../courses.php" class="quick-action-btn">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-search me-3 text-primary"></i>
                            <div>
                                <div class="fw-medium">تصفح الدورات</div>
                                <small class="text-muted">اكتشف دورات جديدة</small>
                            </div>
                        </div>
                    </a>
                    <a href="certificates-simple.php" class="quick-action-btn">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-certificate me-3 text-success"></i>
                            <div>
                                <div class="fw-medium">شهاداتي</div>
                                <small class="text-muted">عرض الشهادات المحصلة</small>
                            </div>
                        </div>
                    </a>
                    <a href="profile.php" class="quick-action-btn">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user me-3 text-info"></i>
                            <div>
                                <div class="fw-medium">الملف الشخصي</div>
                                <small class="text-muted">تحديث البيانات</small>
                            </div>
                        </div>
                    </a>
                    <a href="#support" class="quick-action-btn">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-headset me-3 text-warning"></i>
                            <div>
                                <div class="fw-medium">الدعم الفني</div>
                                <small class="text-muted">تواصل معنا</small>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Achievement Badge -->
            <div class="sidebar-card text-center" style="background: var(--success-gradient); color: white;">
                <i class="fas fa-trophy mb-3" style="font-size: 3rem; opacity: 0.8;"></i>
                <h6 class="fw-bold mb-2">إنجاز رائع!</h6>
                <p class="mb-3 opacity-90">لقد أكملت <?php echo $completed_lessons; ?> درس حتى الآن</p>
                <div class="badge bg-light text-dark">
                    متعلم نشط
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Spacer before footer -->
<div style="height: 80px;"></div>

<!-- Footer -->
<footer style="background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%); color: white; padding: 4rem 0 2rem;">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-4 mb-4">
                <div class="mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-graduation-cap me-2" style="color: #667eea;"></i>
                        منصة التعليم التفاعلي
                    </h5>
                    <p class="text-light opacity-75 mb-4">منصة تعليمية متقدمة تهدف إلى توفير أفضل تجربة تعلم تفاعلية للطلاب في العالم العربي مع أحدث التقنيات التعليمية.</p>
                </div>
                <div class="d-flex gap-3">
                    <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;">
                        <i class="fab fa-facebook"></i>
                    </a>
                    <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;">
                        <i class="fab fa-linkedin"></i>
                    </a>
                </div>
            </div>

            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="fw-bold mb-3" style="color: #667eea;">روابط سريعة</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <a href="../index.php" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="../courses.php" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            الدورات
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#about" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            من نحن
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#contact" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            اتصل بنا
                        </a>
                    </li>
                </ul>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <h6 class="fw-bold mb-3" style="color: #667eea;">الدعم والمساعدة</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <a href="#help" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            مركز المساعدة
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#faq" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            الأسئلة الشائعة
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#privacy" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            سياسة الخصوصية
                        </a>
                    </li>
                    <li class="mb-2">
                        <a href="#terms" class="text-light text-decoration-none opacity-75 d-flex align-items-center">
                            <i class="fas fa-chevron-left me-2" style="font-size: 0.8rem;"></i>
                            شروط الاستخدام
                        </a>
                    </li>
                </ul>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <h6 class="fw-bold mb-3" style="color: #667eea;">تواصل معنا</h6>
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-primary bg-opacity-25 rounded-circle p-2 me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-envelope" style="color: #667eea;"></i>
                        </div>
                        <div>
                            <small class="text-light opacity-75">البريد الإلكتروني</small>
                            <div class="text-light"><EMAIL></div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-primary bg-opacity-25 rounded-circle p-2 me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-phone" style="color: #667eea;"></i>
                        </div>
                        <div>
                            <small class="text-light opacity-75">الهاتف</small>
                            <div class="text-light">+966 123 456 789</div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="bg-primary bg-opacity-25 rounded-circle p-2 me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-map-marker-alt" style="color: #667eea;"></i>
                        </div>
                        <div>
                            <small class="text-light opacity-75">العنوان</small>
                            <div class="text-light">الرياض، السعودية</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <hr class="my-4 opacity-25">

        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 opacity-75">&copy; 2024 منصة التعليم التفاعلي. جميع الحقوق محفوظة.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-0 opacity-75">
                    صُنع بـ <i class="fas fa-heart text-danger"></i> في المملكة العربية السعودية
                </p>
            </div>
        </div>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Chart.js Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Progress Chart
    const ctx = document.getElementById('progressChart').getContext('2d');

    // Create gradient
    const gradient1 = ctx.createLinearGradient(0, 0, 0, 300);
    gradient1.addColorStop(0, '#667eea');
    gradient1.addColorStop(1, '#764ba2');

    const gradient2 = ctx.createLinearGradient(0, 0, 0, 300);
    gradient2.addColorStop(0, '#e2e8f0');
    gradient2.addColorStop(1, '#cbd5e0');

    const progressChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['مكتمل', 'متبقي'],
            datasets: [{
                data: [<?php echo round($average_progress); ?>, <?php echo 100 - round($average_progress); ?>],
                backgroundColor: [gradient1, gradient2],
                borderColor: ['#667eea', '#e2e8f0'],
                borderWidth: 3,
                hoverBorderWidth: 5,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            aspectRatio: 1,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 25,
                        usePointStyle: true,
                        pointStyle: 'circle',
                        font: {
                            family: 'Cairo',
                            size: 14,
                            weight: '600'
                        },
                        color: '#4a5568'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + '%';
                        }
                    }
                }
            },
            cutout: '65%',
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2000,
                easing: 'easeInOutQuart'
            },
            elements: {
                arc: {
                    borderRadius: 8
                }
            }
        }
    });

    // Add percentage text in center
    const centerText = {
        id: 'centerText',
        beforeDatasetsDraw(chart, args, options) {
            const { ctx, data } = chart;
            ctx.save();

            // Main percentage
            ctx.font = 'bold 32px Cairo';
            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            const centerX = (chart.chartArea.left + chart.chartArea.right) / 2;
            const centerY = (chart.chartArea.top + chart.chartArea.bottom) / 2;
            ctx.fillText('<?php echo round($average_progress); ?>%', centerX, centerY - 10);

            // Subtitle
            ctx.font = '600 14px Cairo';
            ctx.fillStyle = '#718096';
            ctx.fillText('مكتمل', centerX, centerY + 20);

            ctx.restore();
        }
    };

    Chart.register(centerText);

    // Add hover effects
    ctx.canvas.addEventListener('mousemove', function(e) {
        const points = progressChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, true);
        if (points.length) {
            ctx.canvas.style.cursor = 'pointer';
        } else {
            ctx.canvas.style.cursor = 'default';
        }
    });
});
</script>

</body>
</html>