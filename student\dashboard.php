<?php
require_once '../config/config.php';
require_once '../config/database.php';

// Check if user is logged in and is a student
if (!isAuthenticated() || $_SESSION['user_role'] !== 'student') {
    redirect('../login.php');
}

// Get student's enrolled courses
$enrolled_courses = fetchAll("
    SELECT 
        c.*,
        u.full_name as instructor_name,
        COUNT(DISTINCT l.id) as total_lessons,
        COUNT(DISTINCT cl.id) as completed_lessons,
        (COUNT(DISTINCT cl.id) * 100 / COUNT(DISTINCT l.id)) as progress
    FROM enrollments e
    JOIN courses c ON e.course_id = c.id
    JOIN users u ON c.instructor_id = u.id
    LEFT JOIN lessons l ON c.id = l.course_id
    LEFT JOIN completed_lessons cl ON l.id = cl.lesson_id AND cl.user_id = ?
    WHERE e.user_id = ?
    GROUP BY c.id
    ORDER BY e.created_at DESC
", [$_SESSION['user_id'], $_SESSION['user_id']]);

// Set page title
$page_title = 'لوحة الطالب';

// Include header
require_once 'header.php';

// Get recent activities
$activities = fetchAll("
    SELECT 
        a.*,
        c.title as course_title,
        l.title as lesson_title
    FROM activities a
    LEFT JOIN courses c ON a.course_id = c.id
    LEFT JOIN lessons l ON a.lesson_id = l.id
    WHERE a.user_id = ?
    ORDER BY a.created_at DESC
    LIMIT 5
", [$_SESSION['user_id']]);
?>

<!-- Dashboard Header -->
<section class="bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">مرحباً، <?php echo $_SESSION['user_name']; ?></h1>
                <p class="lead mb-4">
                    استمر في رحلة التعلم واكتشف المزيد من الدورات
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="../courses.php" class="btn btn-light">
                    <i class="fas fa-search me-2"></i>
                    استكشف دورات جديدة
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Enrolled Courses -->
            <div class="col-lg-8">
                <h2 class="mb-4">دوراتي</h2>
                
                <?php if (empty($enrolled_courses)): ?>
                    <div class="alert alert-info">
                        لم تقم بالتسجيل في أي دورة بعد. 
                        <a href="../courses.php" class="alert-link">استكشف الدورات المتاحة</a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($enrolled_courses as $course): ?>
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <img src="<?php echo $course['thumbnail'] ?? '../assets/images/default-course.jpg'; ?>" 
                                         class="card-img-top" 
                                         alt="<?php echo $course['title']; ?>"
                                         style="height: 200px; object-fit: cover;">
                                    
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo $course['title']; ?></h5>
                                        <p class="text-muted mb-3"><?php echo $course['instructor_name']; ?></p>
                                        
                                        <div class="progress mb-3">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: <?php echo $course['progress']; ?>%"
                                                 aria-valuenow="<?php echo $course['progress']; ?>" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100">
                                                <?php echo round($course['progress']); ?>%
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between text-muted mb-3">
                                            <small>
                                                <i class="fas fa-play-circle me-1"></i>
                                                <?php echo $course['completed_lessons']; ?>/<?php echo $course['total_lessons']; ?> درس
                                            </small>
                                            <small>
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo $course['duration']; ?> ساعة
                                            </small>
                                        </div>
                                        
                                        <a href="../course.php?id=<?php echo $course['id']; ?>" class="btn btn-primary w-100">
                                            متابعة التعلم
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Recent Activities -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h3 class="card-title mb-4">النشاطات الأخيرة</h3>
                        
                        <?php if (empty($activities)): ?>
                            <p class="text-muted mb-0">لا توجد نشاطات حديثة</p>
                        <?php else: ?>
                            <div class="timeline">
                                <?php foreach ($activities as $activity): ?>
                                    <div class="timeline-item mb-3">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-circle text-primary"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <p class="mb-1"><?php echo $activity['description']; ?></p>
                                                <small class="text-muted">
                                                    <?php echo date('Y/m/d H:i', strtotime($activity['created_at'])); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title mb-4">إحصائيات سريعة</h3>
                        
                        <div class="row text-center">
                            <div class="col-6 mb-4">
                                <div class="display-4 fw-bold text-primary mb-2">
                                    <?php echo count($enrolled_courses); ?>
                                </div>
                                <p class="text-muted mb-0">الدورات المسجلة</p>
                            </div>
                            <div class="col-6 mb-4">
                                <?php
                                $total_progress = 0;
                                foreach ($enrolled_courses as $course) {
                                    $total_progress += $course['progress'];
                                }
                                $average_progress = count($enrolled_courses) > 0 ? $total_progress / count($enrolled_courses) : 0;
                                ?>
                                <div class="display-4 fw-bold text-primary mb-2">
                                    <?php echo round($average_progress); ?>%
                                </div>
                                <p class="text-muted mb-0">متوسط التقدم</p>
                            </div>
                            <div class="col-6">
                                <?php
                                $total_lessons = 0;
                                $completed_lessons = 0;
                                foreach ($enrolled_courses as $course) {
                                    $total_lessons += $course['total_lessons'];
                                    $completed_lessons += $course['completed_lessons'];
                                }
                                ?>
                                <div class="display-4 fw-bold text-primary mb-2">
                                    <?php echo $completed_lessons; ?>/<?php echo $total_lessons; ?>
                                </div>
                                <p class="text-muted mb-0">الدروس المكتملة</p>
                            </div>
                            <div class="col-6">
                                <?php
                                $total_hours = 0;
                                foreach ($enrolled_courses as $course) {
                                    $total_hours += $course['duration'];
                                }
                                ?>
                                <div class="display-4 fw-bold text-primary mb-2">
                                    <?php echo $total_hours; ?>
                                </div>
                                <p class="text-muted mb-0">ساعات التعلم</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once 'footer.php'; ?>