<?php
require_once 'config/config.php';
require_once 'includes/header.php';

// Get instructor ID from URL
$instructor_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get instructor details
$instructor = fetchOne("
    SELECT 
        u.*,
        COUNT(DISTINCT c.id) as total_courses,
        COUNT(DISTINCT e.id) as total_students,
        AVG(c.rating) as average_rating
    FROM users u
    LEFT JOIN courses c ON u.id = c.instructor_id
    LEFT JOIN enrollments e ON c.id = e.course_id
    WHERE u.id = ? AND u.role = 'instructor'
    GROUP BY u.id
", [$instructor_id]);

if (!$instructor) {
    redirect('instructors.php');
}

// Get instructor's courses
$courses = fetchAll("
    SELECT 
        c.*,
        COUNT(DISTINCT e.id) as enrolled_students,
        AVG(r.rating) as average_rating
    FROM courses c
    LEFT JOIN enrollments e ON c.id = e.course_id
    LEFT JOIN reviews r ON c.id = r.course_id
    WHERE c.instructor_id = ? AND c.status = 'published'
    GROUP BY c.id
    ORDER BY c.created_at DESC
", [$instructor_id]);
?>

<!-- Instructor Profile -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Instructor Info -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-body text-center">
                        <img src="<?php echo $instructor['avatar'] ?? 'assets/images/default-avatar.png'; ?>" 
                             alt="<?php echo $instructor['full_name']; ?>" 
                             class="rounded-circle mb-3" 
                             style="width: 150px; height: 150px; object-fit: cover;">
                        
                        <h4 class="card-title mb-1"><?php echo $instructor['full_name']; ?></h4>
                        <p class="text-muted mb-3"><?php echo $instructor['bio'] ?? 'مدرس محترف'; ?></p>
                        
                        <div class="d-flex justify-content-center mb-3">
                            <?php if ($instructor['average_rating']): ?>
                                <div class="me-3">
                                    <i class="fas fa-star text-warning"></i>
                                    <?php echo number_format($instructor['average_rating'], 1); ?>
                                </div>
                            <?php endif; ?>
                            <div class="me-3">
                                <i class="fas fa-users text-primary"></i>
                                <?php echo $instructor['total_students']; ?> طالب
                            </div>
                            <div>
                                <i class="fas fa-book text-success"></i>
                                <?php echo $instructor['total_courses']; ?> دورة
                            </div>
                        </div>
                        
                        <?php if ($instructor['social_links']): ?>
                            <div class="social-links mb-3">
                                <?php foreach (json_decode($instructor['social_links'], true) as $platform => $link): ?>
                                    <a href="<?php echo $link; ?>" class="btn btn-outline-primary btn-sm me-2" target="_blank">
                                        <i class="fab fa-<?php echo $platform; ?>"></i>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                        
                        <a href="contact.php?instructor=<?php echo $instructor['id']; ?>" class="btn btn-primary">
                            تواصل مع المدرس
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Instructor's Courses -->
            <div class="col-lg-8">
                <h3 class="mb-4">الدورات التعليمية</h3>
                
                <?php if (empty($courses)): ?>
                    <div class="alert alert-info">
                        لا توجد دورات متاحة حالياً
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($courses as $course): ?>
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <img src="<?php echo $course['thumbnail'] ?? 'assets/images/default-course.jpg'; ?>" 
                                         class="card-img-top" 
                                         alt="<?php echo $course['title']; ?>"
                                         style="height: 200px; object-fit: cover;">
                                    
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo $course['title']; ?></h5>
                                        <p class="card-text text-muted">
                                            <?php echo substr($course['description'], 0, 100) . '...'; ?>
                                        </p>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <?php if ($course['average_rating']): ?>
                                                    <i class="fas fa-star text-warning"></i>
                                                    <?php echo number_format($course['average_rating'], 1); ?>
                                                <?php endif; ?>
                                                <small class="text-muted ms-2">
                                                    <?php echo $course['enrolled_students']; ?> طالب
                                                </small>
                                            </div>
                                            <div>
                                                <?php if ($course['price'] > 0): ?>
                                                    <span class="text-primary fw-bold">
                                                        <?php echo number_format($course['price'], 2); ?> ريال
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-success">مجاني</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="card-footer bg-white">
                                        <a href="course.php?id=<?php echo $course['id']; ?>" class="btn btn-primary w-100">
                                            عرض الدورة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?> 