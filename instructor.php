<?php
require_once 'config/config.php';
require_once 'config/database_auto.php';

// Get instructor ID from URL
$instructor_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($instructor_id <= 0) {
    header('Location: instructors.php');
    exit;
}

// Get instructor details
$instructor = fetch("
    SELECT
        u.*,
        COUNT(DISTINCT c.id) as total_courses,
        COUNT(DISTINCT e.id) as total_students,
        AVG(c.rating) as average_rating
    FROM users u
    LEFT JOIN courses c ON u.id = c.instructor_id
    LEFT JOIN enrollments e ON c.id = e.course_id
    WHERE u.id = ? AND u.role = 'instructor'
    GROUP BY u.id
", [$instructor_id]);

if (!$instructor) {
    header('Location: instructors.php');
    exit;
}

// Get instructor's courses
$courses = fetchAll("
    SELECT 
        c.*,
        COUNT(DISTINCT e.id) as enrolled_students,
        AVG(r.rating) as average_rating
    FROM courses c
    LEFT JOIN enrollments e ON c.id = e.course_id
    LEFT JOIN reviews r ON c.id = r.course_id
    WHERE c.instructor_id = ? AND c.status = 'published'
    GROUP BY c.id
    ORDER BY c.created_at DESC
", [$instructor_id]);

$page_title = 'الملف الشخصي - ' . $instructor['full_name'];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - منصة التعليم التفاعلي</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
            --shadow-medium: 0 10px 30px rgba(0,0,0,0.1);
            --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
            --border-radius: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: var(--shadow-medium);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: white !important;
        }

        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            margin: 0 5px;
            padding: 8px 16px !important;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white !important;
            transform: translateY(-1px);
        }

        .hero-section {
            background: var(--primary-gradient);
            color: white;
            padding: 4rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .instructor-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-heavy);
            padding: 2rem;
            margin-top: -3rem;
            position: relative;
            z-index: 2;
            border: none;
            transition: var(--transition);
        }

        .instructor-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
        }

        .instructor-avatar {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            border: 5px solid white;
            box-shadow: var(--shadow-medium);
            object-fit: cover;
            transition: var(--transition);
        }

        .instructor-avatar:hover {
            transform: scale(1.05);
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            border: none;
            margin-bottom: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stats-icon.primary { background: var(--primary-gradient); }
        .stats-icon.success { background: var(--success-gradient); }
        .stats-icon.warning { background: var(--warning-gradient); }

        .course-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
            border: none;
            height: 100%;
        }

        .course-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-heavy);
        }

        .course-thumbnail {
            height: 220px;
            object-fit: cover;
            transition: var(--transition);
            width: 100%;
        }

        .course-card:hover .course-thumbnail {
            transform: scale(1.1);
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition);
        }

        .btn-gradient:hover::before {
            left: 100%;
        }

        .btn-gradient:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .social-links a {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 0 5px;
            transition: var(--transition);
            text-decoration: none;
        }

        .social-links a:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
        }

        .section-title {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 2rem;
            position: relative;
            padding-bottom: 1rem;
            font-size: 2rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .rating-stars {
            color: #ffc107;
            font-size: 1.1rem;
        }

        .price-tag {
            background: var(--success-gradient);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .price-tag.free {
            background: var(--warning-gradient);
        }

        .breadcrumb {
            background: transparent;
            padding: 1rem 0;
        }

        .breadcrumb-item a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: white;
        }

        /* Footer Styles */
        footer {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%) !important;
        }

        footer h5, footer h6 {
            color: #e2e8f0 !important;
            font-weight: 600;
        }

        footer .text-light {
            color: #cbd5e0 !important;
            transition: var(--transition);
        }

        footer .text-light:hover {
            color: #667eea !important;
            text-decoration: none;
        }

        footer .fab, footer .fas {
            transition: var(--transition);
        }

        footer .fab:hover, footer .fas:hover {
            transform: translateY(-2px);
            color: #667eea !important;
        }

        footer .input-group .form-control {
            border-radius: 25px 0 0 25px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        footer .input-group .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        footer .input-group .btn {
            border-radius: 0 25px 25px 0;
            background: var(--primary-gradient);
            border: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-section {
                padding: 2rem 0 1rem;
            }

            .instructor-avatar {
                width: 150px;
                height: 150px;
            }

            .instructor-card {
                margin-top: -2rem;
                padding: 1.5rem;
            }

            .section-title {
                font-size: 1.5rem;
            }

            footer .col-lg-4, footer .col-lg-2 {
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-graduation-cap me-2"></i>
            منصة التعليم التفاعلي
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="index.php">الرئيسية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="courses.php">الدورات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="instructors.php">المدربين</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.php">من نحن</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.php">اتصل بنا</a>
                </li>
            </ul>

            <div class="d-flex">
                <?php if (isAuthenticated()): ?>
                    <a href="student/dashboard.php" class="btn btn-outline-light me-2">لوحة التحكم</a>
                    <a href="logout.php" class="btn btn-light">تسجيل الخروج</a>
                <?php else: ?>
                    <a href="login.php" class="btn btn-outline-light me-2">تسجيل الدخول</a>
                    <a href="register.php" class="btn btn-light">إنشاء حساب</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="instructors.php">المدربين</a></li>
                <li class="breadcrumb-item active"><?php echo htmlspecialchars($instructor['full_name']); ?></li>
            </ol>
        </nav>

        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-3">الملف الشخصي للمدرب</h1>
                <p class="lead mb-0">تعرف على خبرات ودورات المدرب المتميز</p>
            </div>
            <div class="col-md-4 text-center">
                <i class="fas fa-chalkboard-teacher" style="font-size: 5rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Instructor Profile -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Instructor Info -->
            <div class="col-lg-4 mb-4">
                <div class="instructor-card text-center">
                    <img src="<?php echo $instructor['profile_image'] ? $instructor['profile_image'] : 'assets/images/default-avatar.png'; ?>"
                         alt="<?php echo htmlspecialchars($instructor['full_name']); ?>"
                         class="instructor-avatar mb-4"
                         onerror="this.src='assets/images/default-avatar.png'">

                    <h2 class="fw-bold mb-2"><?php echo htmlspecialchars($instructor['full_name']); ?></h2>
                    <p class="text-muted mb-4 fs-5"><?php echo htmlspecialchars($instructor['bio'] ?? 'مدرب محترف ومتخصص'); ?></p>

                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-4">
                            <div class="stats-card">
                                <div class="stats-icon primary">
                                    <i class="fas fa-star"></i>
                                </div>
                                <h4 class="fw-bold mb-1"><?php echo $instructor['average_rating'] ? number_format($instructor['average_rating'], 1) : '5.0'; ?></h4>
                                <small class="text-muted">التقييم</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stats-card">
                                <div class="stats-icon success">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h4 class="fw-bold mb-1"><?php echo number_format($instructor['total_students']); ?></h4>
                                <small class="text-muted">طالب</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stats-card">
                                <div class="stats-icon warning">
                                    <i class="fas fa-book"></i>
                                </div>
                                <h4 class="fw-bold mb-1"><?php echo $instructor['total_courses']; ?></h4>
                                <small class="text-muted">دورة</small>
                            </div>
                        </div>
                    </div>

                    <!-- Social Links -->
                    <div class="social-links mb-4">
                        <a href="#" class="btn btn-outline-primary">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="btn btn-outline-info">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-danger">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="btn btn-outline-primary">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>

                    <!-- Contact Button -->
                    <a href="contact.php?instructor=<?php echo $instructor['id']; ?>" class="btn btn-gradient w-100 mb-3">
                        <i class="fas fa-envelope me-2"></i>تواصل مع المدرب
                    </a>

                    <!-- Additional Info -->
                    <div class="text-start">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-calendar-alt text-primary me-2"></i>
                            <span>عضو منذ <?php echo date('Y', strtotime($instructor['created_at'])); ?></span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-envelope text-primary me-2"></i>
                            <span><?php echo htmlspecialchars($instructor['email']); ?></span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-graduation-cap text-primary me-2"></i>
                            <span>مدرب معتمد</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Instructor's Courses -->
            <div class="col-lg-8">
                <h2 class="section-title">الدورات التعليمية</h2>

                <?php if (empty($courses)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-book-open text-muted" style="font-size: 4rem; opacity: 0.5;"></i>
                        <h4 class="text-muted mt-3">لا توجد دورات متاحة حالياً</h4>
                        <p class="text-muted">سيتم إضافة دورات جديدة قريباً</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($courses as $course): ?>
                            <div class="col-md-6 mb-4">
                                <div class="course-card">
                                    <div class="position-relative overflow-hidden">
                                        <img src="<?php echo $course['thumbnail'] ?? 'assets/images/default-course.jpg'; ?>"
                                             class="course-thumbnail"
                                             alt="<?php echo htmlspecialchars($course['title']); ?>"
                                             onerror="this.src='assets/images/default-course.jpg'">

                                        <!-- Course Level Badge -->
                                        <div class="position-absolute top-0 start-0 m-3">
                                            <span class="badge bg-primary"><?php echo ucfirst($course['level'] ?? 'مبتدئ'); ?></span>
                                        </div>

                                        <!-- Price Badge -->
                                        <div class="position-absolute top-0 end-0 m-3">
                                            <?php if ($course['price'] > 0): ?>
                                                <span class="price-tag">
                                                    <?php echo number_format($course['price'], 0); ?> ريال
                                                </span>
                                            <?php else: ?>
                                                <span class="price-tag free">مجاني</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="p-4">
                                        <h5 class="fw-bold mb-3"><?php echo htmlspecialchars($course['title']); ?></h5>
                                        <p class="text-muted mb-3">
                                            <?php echo htmlspecialchars(substr($course['description'], 0, 120)) . '...'; ?>
                                        </p>

                                        <!-- Course Stats -->
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div class="d-flex align-items-center">
                                                <?php if ($course['average_rating']): ?>
                                                    <div class="rating-stars me-2">
                                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                                            <i class="fas fa-star<?php echo $i <= $course['average_rating'] ? '' : '-o'; ?>"></i>
                                                        <?php endfor; ?>
                                                    </div>
                                                    <span class="text-muted">(<?php echo number_format($course['average_rating'], 1); ?>)</span>
                                                <?php else: ?>
                                                    <span class="text-muted">جديد</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="text-muted">
                                                <i class="fas fa-users me-1"></i>
                                                <?php echo number_format($course['enrolled_students']); ?> طالب
                                            </div>
                                        </div>

                                        <!-- Course Duration -->
                                        <?php if ($course['duration']): ?>
                                            <div class="d-flex align-items-center mb-3 text-muted">
                                                <i class="fas fa-clock me-2"></i>
                                                <span><?php echo floor($course['duration'] / 60); ?> ساعة</span>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Action Button -->
                                        <a href="course.php?id=<?php echo $course['id']; ?>" class="btn btn-gradient w-100">
                                            <i class="fas fa-play me-2"></i>عرض الدورة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Show More Button -->
                    <?php if (count($courses) >= 6): ?>
                        <div class="text-center mt-4">
                            <a href="courses.php?instructor=<?php echo $instructor['id']; ?>" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-plus me-2"></i>عرض المزيد من الدورات
                            </a>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="bg-dark text-white mt-5">
    <div class="container py-5">
        <div class="row">
            <!-- About Section -->
            <div class="col-lg-4 col-md-6 mb-4">
                <h5 class="mb-3">
                    <i class="fas fa-graduation-cap me-2"></i>
                    منصة التعليم التفاعلي
                </h5>
                <p class="text-light mb-3">
                    منصة تعليمية متطورة تهدف إلى تقديم أفضل تجربة تعليمية تفاعلية للطلاب في الوطن العربي
                    من خلال دورات عالية الجودة ومحتوى تعليمي متميز.
                </p>
                <div class="d-flex">
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-facebook"></i></a>
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-instagram"></i></a>
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="text-light fs-5"><i class="fab fa-youtube"></i></a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="mb-3">روابط سريعة</h6>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="index.php" class="text-light text-decoration-none">الرئيسية</a></li>
                    <li class="mb-2"><a href="courses.php" class="text-light text-decoration-none">الدورات</a></li>
                    <li class="mb-2"><a href="instructors.php" class="text-light text-decoration-none">المدربين</a></li>
                    <li class="mb-2"><a href="about.php" class="text-light text-decoration-none">من نحن</a></li>
                </ul>
            </div>

            <!-- Support -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="mb-3">الدعم</h6>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="#" class="text-light text-decoration-none">مركز المساعدة</a></li>
                    <li class="mb-2"><a href="#" class="text-light text-decoration-none">الأسئلة الشائعة</a></li>
                    <li class="mb-2"><a href="contact.php" class="text-light text-decoration-none">اتصل بنا</a></li>
                    <li class="mb-2"><a href="#" class="text-light text-decoration-none">بلاغ مشكلة</a></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div class="col-lg-4 col-md-6 mb-4">
                <h6 class="mb-3">معلومات التواصل</h6>
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-envelope me-2"></i>
                    <span><EMAIL></span>
                </div>
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-phone me-2"></i>
                    <span>+966 11 123 4567</span>
                </div>
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <span>الرياض، المملكة العربية السعودية</span>
                </div>

                <!-- Newsletter -->
                <div class="mt-3">
                    <h6 class="mb-2">اشترك في النشرة الإخبارية</h6>
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="بريدك الإلكتروني">
                        <button class="btn btn-primary" type="button">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <hr class="my-4">

        <!-- Bottom Footer -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0">&copy; 2024 منصة التعليم التفاعلي. جميع الحقوق محفوظة.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="#" class="text-light text-decoration-none me-3">سياسة الخصوصية</a>
                <a href="#" class="text-light text-decoration-none me-3">شروط الاستخدام</a>
                <a href="#" class="text-light text-decoration-none">ملفات تعريف الارتباط</a>
            </div>
        </div>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Add smooth scrolling
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});

// Add loading animation for course cards
document.addEventListener('DOMContentLoaded', function() {
    const courseCards = document.querySelectorAll('.course-card');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });

    courseCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});

// Add hover effects for social links
document.querySelectorAll('.social-links a').forEach(link => {
    link.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-3px) scale(1.1)';
    });

    link.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});
</script>

</body>
</html>