<?php
session_start();
require_once '../config/config.php';
require_once '../config/database.php';

// Check if user is logged in and is an instructor
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'instructor') {
    header('Location: ../login.php');
    exit;
}

$instructor_id = $_SESSION['user_id'];
$success = '';
$error = '';

// Get categories for dropdown
try {
    $categories = fetchAll("SELECT * FROM categories ORDER BY name ASC");
} catch (Exception $e) {
    $categories = [];
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $category_id = (int)$_POST['category_id'];
    $price = (float)$_POST['price'];
    $discount_price = !empty($_POST['discount_price']) ? (float)$_POST['discount_price'] : null;
    $level = $_POST['level'];
    $language = $_POST['language'];
    $duration = !empty($_POST['duration']) ? (int)$_POST['duration'] : null;
    
    // Validate input
    if (empty($title) || empty($description) || $category_id <= 0) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif ($price < 0) {
        $error = 'السعر يجب أن يكون رقم موجب';
    } elseif ($discount_price && $discount_price >= $price) {
        $error = 'سعر الخصم يجب أن يكون أقل من السعر الأصلي';
    } else {
        // Generate slug from title
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
        $original_slug = $slug;
        $counter = 1;
        
        // Make sure slug is unique
        while (fetch("SELECT id FROM courses WHERE slug = ?", [$slug])) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        // Handle thumbnail upload
        $thumbnail_path = null;
        if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/courses/';
            
            // Create directory if it doesn't exist
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $file = $_FILES['thumbnail'];
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            $max_size = 5 * 1024 * 1024; // 5MB
            
            if (in_array($file['type'], $allowed_types) && $file['size'] <= $max_size) {
                $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                $new_filename = 'course_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
                $upload_path = $upload_dir . $new_filename;
                
                if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                    $thumbnail_path = 'uploads/courses/' . $new_filename;
                }
            }
        }
        
        try {
            // Insert course into database
            $course_data = [
                'title' => $title,
                'slug' => $slug,
                'description' => $description,
                'instructor_id' => $instructor_id,
                'category_id' => $category_id,
                'price' => $price,
                'discount_price' => $discount_price,
                'thumbnail' => $thumbnail_path,
                'level' => $level,
                'language' => $language,
                'duration' => $duration,
                'status' => 'draft'
            ];
            
            $course_id = insert('courses', $course_data);
            
            if ($course_id) {
                $success = 'تم إنشاء الدورة بنجاح! يمكنك الآن إضافة الدروس إليها.';
                // Clear form data
                $_POST = [];
            } else {
                $error = 'حدث خطأ في إنشاء الدورة';
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ في إنشاء الدورة: ' . $e->getMessage();
        }
    }
}

$page_title = 'إضافة دورة جديدة';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
            --shadow-medium: 0 10px 30px rgba(0,0,0,0.1);
            --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
            --border-radius: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: var(--shadow-medium);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: white !important;
        }

        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            margin: 0 5px;
            padding: 8px 16px !important;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white !important;
            transform: translateY(-1px);
        }

        .hero-section {
            background: var(--primary-gradient);
            color: white;
            padding: 3rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .form-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-heavy);
            padding: 2.5rem;
            margin-top: -2rem;
            position: relative;
            z-index: 2;
            border: none;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            transition: var(--transition);
            font-size: 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition);
        }

        .btn-gradient:hover::before {
            left: 100%;
        }

        .btn-gradient:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .section-title {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 2rem;
            position: relative;
            padding-bottom: 1rem;
            font-size: 2rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .file-upload-area {
            border: 2px dashed #667eea;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.05);
        }

        .file-upload-area.dragover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.1);
        }

        .breadcrumb {
            background: transparent;
            padding: 1rem 0;
        }

        .breadcrumb-item a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: white;
        }

        .form-step {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-light);
            border-left: 4px solid #667eea;
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-gradient);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 1rem;
        }

        .price-input-group {
            position: relative;
        }

        .price-input-group .form-control {
            padding-right: 50px;
        }

        .price-currency {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #718096;
            font-weight: 600;
        }

        /* Footer Styles */
        footer {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%) !important;
        }

        footer h5, footer h6 {
            color: #e2e8f0 !important;
            font-weight: 600;
        }

        footer .text-light {
            color: #cbd5e0 !important;
            transition: var(--transition);
        }

        footer .text-light:hover {
            color: #667eea !important;
            text-decoration: none;
        }

        footer .fab, footer .fas {
            transition: var(--transition);
        }

        footer .fab:hover, footer .fas:hover {
            transform: translateY(-2px);
            color: #667eea !important;
        }

        footer .input-group .form-control {
            border-radius: 25px 0 0 25px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        footer .input-group .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        footer .input-group .btn {
            border-radius: 0 25px 25px 0;
            background: var(--primary-gradient);
            border: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-section {
                padding: 2rem 0 1rem;
            }

            .form-card {
                margin-top: -1rem;
                padding: 1.5rem;
            }

            .section-title {
                font-size: 1.5rem;
            }

            .step-header {
                flex-direction: column;
                text-align: center;
            }

            .step-number {
                margin-left: 0;
                margin-bottom: 0.5rem;
            }

            footer .col-lg-4, footer .col-lg-2 {
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="../index.php">
            <i class="fas fa-graduation-cap me-2"></i>
            منصة التعليم التفاعلي
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">لوحة التحكم</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="courses.php">دوراتي</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="add-course.php">إضافة دورة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="students.php">الطلاب</a>
                </li>
            </ul>

            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn btn-link text-white text-decoration-none dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2"></i>
                        <?php echo htmlspecialchars($_SESSION['user_name']); ?>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i> الملف الشخصي
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="dashboard.php">لوحة التحكم</a></li>
                <li class="breadcrumb-item active">إضافة دورة جديدة</li>
            </ol>
        </nav>

        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-3">إضافة دورة جديدة</h1>
                <p class="lead mb-0">أنشئ دورة تعليمية جديدة وشاركها مع الطلاب</p>
            </div>
            <div class="col-md-4 text-center">
                <i class="fas fa-plus-circle" style="font-size: 5rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Add Course Form -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-card">
                    <!-- Alerts -->
                    <?php if ($success): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="" enctype="multipart/form-data" class="needs-validation" novalidate>
                        <!-- Step 1: Basic Information -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">1</div>
                                <h4 class="mb-0">المعلومات الأساسية</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="title" class="form-label">عنوان الدورة *</label>
                                    <input type="text" class="form-control" id="title" name="title"
                                           value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>"
                                           placeholder="أدخل عنوان الدورة" required>
                                    <div class="invalid-feedback">يرجى إدخال عنوان الدورة</div>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="category_id" class="form-label">الفئة *</label>
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">اختر الفئة</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>"
                                                    <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار فئة الدورة</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">وصف الدورة *</label>
                                <textarea class="form-control" id="description" name="description" rows="5"
                                          placeholder="اكتب وصفاً شاملاً للدورة وما سيتعلمه الطلاب" required><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                                <div class="invalid-feedback">يرجى إدخال وصف الدورة</div>
                            </div>
                        </div>

                        <!-- Step 2: Course Details -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">2</div>
                                <h4 class="mb-0">تفاصيل الدورة</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="level" class="form-label">مستوى الدورة</label>
                                    <select class="form-select" id="level" name="level">
                                        <option value="beginner" <?php echo (isset($_POST['level']) && $_POST['level'] == 'beginner') ? 'selected' : ''; ?>>مبتدئ</option>
                                        <option value="intermediate" <?php echo (isset($_POST['level']) && $_POST['level'] == 'intermediate') ? 'selected' : ''; ?>>متوسط</option>
                                        <option value="advanced" <?php echo (isset($_POST['level']) && $_POST['level'] == 'advanced') ? 'selected' : ''; ?>>متقدم</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="language" class="form-label">لغة الدورة</label>
                                    <select class="form-select" id="language" name="language">
                                        <option value="Arabic" <?php echo (isset($_POST['language']) && $_POST['language'] == 'Arabic') ? 'selected' : ''; ?>>العربية</option>
                                        <option value="English" <?php echo (isset($_POST['language']) && $_POST['language'] == 'English') ? 'selected' : ''; ?>>الإنجليزية</option>
                                        <option value="French" <?php echo (isset($_POST['language']) && $_POST['language'] == 'French') ? 'selected' : ''; ?>>الفرنسية</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="duration" class="form-label">مدة الدورة (بالدقائق)</label>
                                    <input type="number" class="form-control" id="duration" name="duration"
                                           value="<?php echo htmlspecialchars($_POST['duration'] ?? ''); ?>"
                                           placeholder="مثال: 120" min="1">
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Pricing -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">3</div>
                                <h4 class="mb-0">التسعير</h4>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="price" class="form-label">سعر الدورة *</label>
                                    <div class="price-input-group">
                                        <input type="number" class="form-control" id="price" name="price"
                                               value="<?php echo htmlspecialchars($_POST['price'] ?? '0'); ?>"
                                               placeholder="0.00" min="0" step="0.01" required>
                                        <span class="price-currency">ريال</span>
                                    </div>
                                    <small class="text-muted">أدخل 0 للدورات المجانية</small>
                                    <div class="invalid-feedback">يرجى إدخال سعر الدورة</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="discount_price" class="form-label">سعر الخصم (اختياري)</label>
                                    <div class="price-input-group">
                                        <input type="number" class="form-control" id="discount_price" name="discount_price"
                                               value="<?php echo htmlspecialchars($_POST['discount_price'] ?? ''); ?>"
                                               placeholder="0.00" min="0" step="0.01">
                                        <span class="price-currency">ريال</span>
                                    </div>
                                    <small class="text-muted">يجب أن يكون أقل من السعر الأصلي</small>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4: Course Thumbnail -->
                        <div class="form-step">
                            <div class="step-header">
                                <div class="step-number">4</div>
                                <h4 class="mb-0">صورة الدورة</h4>
                            </div>

                            <div class="mb-3">
                                <label for="thumbnail" class="form-label">صورة مصغرة للدورة</label>
                                <div class="file-upload-area" id="fileUploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h5>اسحب وأفلت الصورة هنا أو انقر للاختيار</h5>
                                    <p class="text-muted mb-0">الحد الأقصى: 5MB | الأنواع المدعومة: JPG, PNG, GIF, WebP</p>
                                    <input type="file" class="form-control d-none" id="thumbnail" name="thumbnail"
                                           accept="image/jpeg,image/png,image/gif,image/webp">
                                </div>
                                <div id="imagePreview" class="mt-3 d-none">
                                    <img id="previewImg" src="" alt="معاينة الصورة" class="img-fluid rounded" style="max-height: 200px;">
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeImage">
                                            <i class="fas fa-trash me-1"></i>إزالة الصورة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-gradient btn-lg px-5">
                                <i class="fas fa-plus me-2"></i>إنشاء الدورة
                            </button>
                            <a href="courses.php" class="btn btn-outline-secondary btn-lg px-5 ms-3">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="bg-dark text-white mt-5">
    <div class="container py-5">
        <div class="row">
            <!-- About Section -->
            <div class="col-lg-4 col-md-6 mb-4">
                <h5 class="mb-3">
                    <i class="fas fa-graduation-cap me-2"></i>
                    منصة التعليم التفاعلي
                </h5>
                <p class="text-light mb-3">
                    منصة تعليمية متطورة تهدف إلى تقديم أفضل تجربة تعليمية تفاعلية للطلاب في الوطن العربي
                    من خلال دورات عالية الجودة ومحتوى تعليمي متميز.
                </p>
                <div class="d-flex">
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-facebook"></i></a>
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-instagram"></i></a>
                    <a href="#" class="text-light me-3 fs-5"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="text-light fs-5"><i class="fab fa-youtube"></i></a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="mb-3">روابط سريعة</h6>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="dashboard.php" class="text-light text-decoration-none">لوحة التحكم</a></li>
                    <li class="mb-2"><a href="courses.php" class="text-light text-decoration-none">دوراتي</a></li>
                    <li class="mb-2"><a href="add-course.php" class="text-light text-decoration-none">إضافة دورة</a></li>
                    <li class="mb-2"><a href="students.php" class="text-light text-decoration-none">الطلاب</a></li>
                </ul>
            </div>

            <!-- Support -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="mb-3">الدعم</h6>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="#" class="text-light text-decoration-none">مركز المساعدة</a></li>
                    <li class="mb-2"><a href="#" class="text-light text-decoration-none">الأسئلة الشائعة</a></li>
                    <li class="mb-2"><a href="../contact.php" class="text-light text-decoration-none">اتصل بنا</a></li>
                    <li class="mb-2"><a href="#" class="text-light text-decoration-none">بلاغ مشكلة</a></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div class="col-lg-4 col-md-6 mb-4">
                <h6 class="mb-3">معلومات التواصل</h6>
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-envelope me-2"></i>
                    <span><EMAIL></span>
                </div>
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-phone me-2"></i>
                    <span>+966 11 123 4567</span>
                </div>
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <span>الرياض، المملكة العربية السعودية</span>
                </div>

                <!-- Newsletter -->
                <div class="mt-3">
                    <h6 class="mb-2">اشترك في النشرة الإخبارية</h6>
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="بريدك الإلكتروني">
                        <button class="btn btn-primary" type="button">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <hr class="my-4">

        <!-- Bottom Footer -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0">&copy; 2024 منصة التعليم التفاعلي. جميع الحقوق محفوظة.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="#" class="text-light text-decoration-none me-3">سياسة الخصوصية</a>
                <a href="#" class="text-light text-decoration-none me-3">شروط الاستخدام</a>
                <a href="#" class="text-light text-decoration-none">ملفات تعريف الارتباط</a>
            </div>
        </div>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// File upload functionality
const fileUploadArea = document.getElementById('fileUploadArea');
const fileInput = document.getElementById('thumbnail');
const imagePreview = document.getElementById('imagePreview');
const previewImg = document.getElementById('previewImg');
const removeImageBtn = document.getElementById('removeImage');

// Click to select file
fileUploadArea.addEventListener('click', () => {
    fileInput.click();
});

// Drag and drop functionality
fileUploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    fileUploadArea.classList.add('dragover');
});

fileUploadArea.addEventListener('dragleave', () => {
    fileUploadArea.classList.remove('dragover');
});

fileUploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    fileUploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect(files[0]);
    }
});

// File input change
fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        handleFileSelect(e.target.files[0]);
    }
});

// Handle file selection
function handleFileSelect(file) {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        alert('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF, أو WebP');
        return;
    }

    // Validate file size (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
        alert('حجم الملف كبير جداً. الحد الأقصى هو 5MB');
        return;
    }

    // Show preview
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImg.src = e.target.result;
        imagePreview.classList.remove('d-none');
        fileUploadArea.style.display = 'none';
    };
    reader.readAsDataURL(file);
}

// Remove image
removeImageBtn.addEventListener('click', () => {
    fileInput.value = '';
    imagePreview.classList.add('d-none');
    fileUploadArea.style.display = 'block';
});

// Price validation
const priceInput = document.getElementById('price');
const discountPriceInput = document.getElementById('discount_price');

discountPriceInput.addEventListener('input', function() {
    const price = parseFloat(priceInput.value) || 0;
    const discountPrice = parseFloat(this.value) || 0;

    if (discountPrice >= price && price > 0) {
        this.setCustomValidity('سعر الخصم يجب أن يكون أقل من السعر الأصلي');
    } else {
        this.setCustomValidity('');
    }
});

// Auto-generate slug from title (for future use)
const titleInput = document.getElementById('title');
titleInput.addEventListener('input', function() {
    // This could be used to show a preview of the course URL
    const slug = this.value.toLowerCase()
        .replace(/[^a-zA-Z0-9\u0600-\u06FF\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim();
});

// Smooth scrolling for form steps
document.querySelectorAll('.form-step').forEach((step, index) => {
    step.style.opacity = '0';
    step.style.transform = 'translateY(20px)';
    step.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

    setTimeout(() => {
        step.style.opacity = '1';
        step.style.transform = 'translateY(0)';
    }, index * 200);
});

// Add loading state to submit button
const form = document.querySelector('form');
const submitBtn = form.querySelector('button[type="submit"]');

form.addEventListener('submit', function() {
    if (this.checkValidity()) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
        submitBtn.disabled = true;
    }
});

// Auto-hide alerts after 5 seconds
document.querySelectorAll('.alert').forEach(alert => {
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    }, 5000);
});

// Add character counter for description
const descriptionTextarea = document.getElementById('description');
const maxLength = 1000;

descriptionTextarea.addEventListener('input', function() {
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;

    // Create or update counter
    let counter = this.parentNode.querySelector('.char-counter');
    if (!counter) {
        counter = document.createElement('small');
        counter.className = 'char-counter text-muted';
        this.parentNode.appendChild(counter);
    }

    counter.textContent = `${currentLength}/${maxLength} حرف`;

    if (remaining < 50) {
        counter.className = 'char-counter text-warning';
    } else if (remaining < 0) {
        counter.className = 'char-counter text-danger';
    } else {
        counter.className = 'char-counter text-muted';
    }
});
</script>

</body>
</html>
